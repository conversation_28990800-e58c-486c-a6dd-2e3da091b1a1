
import { UUID } from './index';
import { WatercraftType } from './watercraft';
import { WeatherStation, WeatherUnit } from './weather';

export type UserRole = 'member' | 'coach' | 'admin';
export type SkillType = 'Rowing' | 'Coaching' | 'Maintenance' | 'Coxswain';
export type Language = 'en' | 'es' | 'fr' | 'de' | 'ko' | 'he' | 'ru' | 'ja' | 'hi';

export interface WatercraftPermission {
  watercraftType: WatercraftType;
  skillLevel: number; // Now 0-3 instead of 1-5
}

export interface User {
  id: UUID;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  skills: SkillType[];
  permissions: WatercraftPermission[];
  language: Language;
  favorites: UUID[];
  preferredWeatherStation?: WeatherStation;
  preferredWeatherUnit?: WeatherUnit;
  createdAt: Date;
  updatedAt: Date;
  deleted?: boolean;
}
