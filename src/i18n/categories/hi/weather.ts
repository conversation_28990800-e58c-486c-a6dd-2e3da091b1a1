export const weatherTranslations = {
  weather_title: "मौसम",
  weather_station: "मौसम केंद्र",
  weather_temperature: "तापमान",
  weather_water_temp: "पानी का तापमान",
  weather_wind_speed: "हवा",
  weather_wind_gust: "तेज़ हवा",
  weather_wind_direction: "दिशा",
  weather_tide_level: "ज्वार",
  weather_tide: "ज्वार",
  weather_lightning: "बिजली",
  weather_lightning_warning: "बिजली की चेतावनी",
  weather_search_placeholder: "मौसम केंद्र खोजें...",
  weather_no_station_selected: "कोई मौसम केंद्र चुना नहीं गया",
  weather_loading: "मौसम डेटा लोड हो रहा है...",
  weather_error: "मौसम डेटा लोड करने में त्रुटि",
  weather_hourly_forecast: "घंटेवार पूर्वानुमान",
  weather_station_saved: "मौसम केंद्र सहेजा गया",
  weather_unit_saved: "मौसम इकाई सहेजी गई",
  weather_station_setting: "मौसम केंद्र",
  weather_station_setting_description: "अपना पसंदीदा मौसम केंद्र चुनें",
  weather_unit_setting: "मौसम इकाइयां",
  weather_unit_metric: "मीट्रिक (°C, किमी/घंटा)",
  weather_unit_imperial: "इंपीरियल (°F, मील/घंटा)",
  weather_not_available: "उपलब्ध नहीं",
  weather_no_results: "कोई केंद्र नहीं मिला",
  weather_no_forecast_available: "भविष्य का पूर्वानुमान उपलब्ध नहीं",
  weather_forecast_not_available: "पूर्वानुमान डेटा उपलब्ध नहीं",
  weather_now: "अभी",
  
  // Station selector specific translations
  weather_geolocation_not_supported: "इस ब्राउज़र में भौगोलिक स्थिति समर्थित नहीं है",
  weather_no_stations_found_nearby: "आपके स्थान से 100 किमी के दायरे में कोई मौसम केंद्र नहीं मिला",
  weather_found_nearby_stations: "{count} नजदीकी मौसम केंद्र मिले",
  weather_failed_find_nearby: "नजदीकी केंद्र खोजने में असफल",
  weather_location_permission_error: "आपका स्थान प्राप्त नहीं कर सकते। कृपया स्थान अनुमतियां जांचें।",
  weather_find_nearby_stations: "नजदीकी केंद्र खोजें",
  weather_nearby_stations: "नजदीकी केंद्र",
  weather_search_results: "खोज परिणाम",
  weather_popular_stations: "लोकप्रिय केंद्र",
  
  // Capability tooltips
  weather_capability_air_temperature: "हवा का तापमान",
  weather_capability_water_temperature: "पानी का तापमान",
  weather_capability_wind_data: "हवा का डेटा",
  weather_capability_water_level: "पानी का स्तर/ज्वार",
  weather_capability_barometric_pressure: "वायुमंडलीय दबाव",
  weather_capability_visibility: "दृश्यता",
  
  // Unit toggle
  weather_use_metric_units: "मीट्रिक इकाइयों का उपयोग करें",
  weather_use_imperial_units: "इंपीरियल इकाइयों का उपयोग करें",
  
  // Settings related translations
  settings_weather: "मौसम सेटिंग्स",
  settings_weather_description: "अपनी मौसम प्राथमिकताएं कॉन्फ़िगर करें",
  settings_preferred_station: "पसंदीदा मौसम केंद्र",
  settings_unit_system: "माप प्रणाली",
  settings_metric: "मीट्रिक (°C, किमी/घंटा)",
  settings_imperial: "इंपीरियल (°F, मील/घंटा)",
};
