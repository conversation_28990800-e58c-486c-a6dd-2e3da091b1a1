
export const weatherTranslations = {
  weather_title: "Weather",
  weather_station: "Weather Station",
  weather_temperature: "Temperature",
  weather_water_temp: "Water Temp",
  weather_wind_speed: "Wind",
  weather_wind_gust: "Gust",
  weather_wind_direction: "Direction",
  weather_tide_level: "Tide",
  weather_tide: "Tide",
  weather_lightning: "Lightning",
  weather_lightning_warning: "Lightning Warning",
  weather_search_placeholder: "Search for a weather station...",
  weather_no_station_selected: "No weather station selected",
  weather_loading: "Loading weather data...",
  weather_error: "Error loading weather data",
  weather_hourly_forecast: "Hourly Forecast",
  weather_station_saved: "Weather station saved",
  weather_unit_saved: "Weather unit saved",
  weather_station_setting: "Weather Station",
  weather_station_setting_description: "Choose your preferred weather station",
  weather_unit_setting: "Weather Units",
  weather_unit_metric: "Metric (°C, km/h)",
  weather_unit_imperial: "Imperial (°F, mph)",
  weather_not_available: "N/A",
  weather_no_results: "No stations found",
  weather_no_forecast_available: "No future forecast available",
  weather_forecast_not_available: "Forecast data not available",
  weather_now: "Now",

  // Station selector specific translations
  weather_geolocation_not_supported: "Geolocation is not supported by this browser",
  weather_no_stations_found_nearby: "No weather stations found within 100km of your location",
  weather_found_nearby_stations: "Found {count} nearby weather stations",
  weather_failed_find_nearby: "Failed to find nearby stations",
  weather_location_permission_error: "Unable to get your location. Please check location permissions.",
  weather_find_nearby_stations: "Find nearby stations",
  weather_nearby_stations: "Nearby Stations",
  weather_search_results: "Search Results",
  weather_popular_stations: "Popular Stations",

  // Capability tooltips
  weather_capability_air_temperature: "Air Temperature",
  weather_capability_water_temperature: "Water Temperature",
  weather_capability_wind_data: "Wind Data",
  weather_capability_water_level: "Water Level/Tide",
  weather_capability_barometric_pressure: "Barometric Pressure",
  weather_capability_visibility: "Visibility",

  // Unit toggle
  weather_use_metric_units: "Use metric units",
  weather_use_imperial_units: "Use imperial units",

  // Settings related translations
  settings_weather: "Weather Settings",
  settings_weather_description: "Configure your weather preferences",
  settings_preferred_station: "Preferred Weather Station",
  settings_unit_system: "Measurement System",
  settings_metric: "Metric (°C, km/h)",
  settings_imperial: "Imperial (°F, mph)",
};
