
export const weatherTranslations = {
  weather_title: "Météo",
  weather_station: "Station météo",
  weather_temperature: "Température",
  weather_water_temp: "Temp. eau",
  weather_wind_speed: "Vent",
  weather_wind_gust: "<PERSON><PERSON><PERSON>",
  weather_wind_direction: "Direction",
  weather_tide_level: "Mar<PERSON>",
  weather_tide: "Mar<PERSON>",
  weather_lightning: "Foudre",
  weather_lightning_warning: "Alerte foudre",
  weather_search_placeholder: "Rechercher une station météo...",
  weather_no_station_selected: "Aucune station sélectionnée",
  weather_loading: "Chargement des données météo...",
  weather_error: "Erreur lors du chargement des données météo",
  weather_hourly_forecast: "Prévisions horaires",
  weather_station_saved: "Station météo enregistrée",
  weather_unit_saved: "Unité météo enregistrée",
  weather_station_setting: "Station météo",
  weather_station_setting_description: "Sélectionnez votre station météo préférée",
  weather_unit_setting: "Unités météo",
  weather_unit_metric: "Métrique (°C, km/h)",
  weather_unit_imperial: "Impérial (°F, mph)",
  weather_not_available: "N/A",
  weather_no_results: "Aucune station trouvée",
  weather_no_forecast_available: "Aucune prévision future disponible",
  weather_forecast_not_available: "Données de prévision non disponibles",
  weather_now: "Maintenant",

  // Station selector specific translations
  weather_geolocation_not_supported: "La géolocalisation n'est pas prise en charge par ce navigateur",
  weather_no_stations_found_nearby: "Aucune station météo trouvée dans un rayon de 100km",
  weather_found_nearby_stations: "{count} stations météo trouvées à proximité",
  weather_failed_find_nearby: "Échec de la recherche de stations à proximité",
  weather_location_permission_error: "Impossible d'obtenir votre position. Veuillez vérifier les autorisations de localisation.",
  weather_find_nearby_stations: "Trouver des stations à proximité",
  weather_nearby_stations: "Stations à proximité",
  weather_search_results: "Résultats de recherche",
  weather_popular_stations: "Stations populaires",

  // Capability tooltips
  weather_capability_air_temperature: "Température de l'air",
  weather_capability_water_temperature: "Température de l'eau",
  weather_capability_wind_data: "Données de vent",
  weather_capability_water_level: "Niveau d'eau/Marée",
  weather_capability_barometric_pressure: "Pression barométrique",
  weather_capability_visibility: "Visibilité",

  // Unit toggle
  weather_use_metric_units: "Utiliser les unités métriques",
  weather_use_imperial_units: "Utiliser les unités impériales",

  // Settings related translations
  settings_weather: "Paramètres météo",
  settings_weather_description: "Configurez vos préférences météo",
  settings_preferred_station: "Station météo préférée",
  settings_unit_system: "Système de mesure",
  settings_metric: "Métrique (°C, km/h)",
  settings_imperial: "Impérial (°F, mph)",
};
