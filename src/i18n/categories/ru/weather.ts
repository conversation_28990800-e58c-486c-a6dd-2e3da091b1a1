export const weatherTranslations = {
  weather_title: "Погода",
  weather_station: "Метеостанция",
  weather_temperature: "Температура",
  weather_water_temp: "Темп. воды",
  weather_wind_speed: "Ветер",
  weather_wind_gust: "Порыв",
  weather_wind_direction: "Направление",
  weather_tide_level: "Прилив",
  weather_tide: "Прилив",
  weather_lightning: "Молния",
  weather_lightning_warning: "Предупреждение о молнии",
  weather_search_placeholder: "Поиск метеостанции...",
  weather_no_station_selected: "Метеостанция не выбрана",
  weather_loading: "Загрузка данных о погоде...",
  weather_error: "Ошибка загрузки данных о погоде",
  weather_hourly_forecast: "Почасовой прогноз",
  weather_station_saved: "Метеостанция сохранена",
  weather_unit_saved: "Единица измерения сохранена",
  weather_station_setting: "Метеостанция",
  weather_station_setting_description: "Выберите предпочитаемую метеостанцию",
  weather_unit_setting: "Единицы измерения",
  weather_unit_metric: "Метрическая (°C, км/ч)",
  weather_unit_imperial: "Имперская (°F, миль/ч)",
  weather_not_available: "Н/Д",
  weather_no_results: "Станции не найдены",
  weather_no_forecast_available: "Будущий прогноз недоступен",
  weather_forecast_not_available: "Данные прогноза недоступны",
  weather_now: "Сейчас",
  
  // Station selector specific translations
  weather_geolocation_not_supported: "Геолокация не поддерживается этим браузером",
  weather_no_stations_found_nearby: "Метеостанции в радиусе 100км от вашего местоположения не найдены",
  weather_found_nearby_stations: "Найдено {count} ближайших метеостанций",
  weather_failed_find_nearby: "Не удалось найти ближайшие станции",
  weather_location_permission_error: "Невозможно получить ваше местоположение. Проверьте разрешения на геолокацию.",
  weather_find_nearby_stations: "Найти ближайшие станции",
  weather_nearby_stations: "Ближайшие станции",
  weather_search_results: "Результаты поиска",
  weather_popular_stations: "Популярные станции",
  
  // Capability tooltips
  weather_capability_air_temperature: "Температура воздуха",
  weather_capability_water_temperature: "Температура воды",
  weather_capability_wind_data: "Данные о ветре",
  weather_capability_water_level: "Уровень воды/Прилив",
  weather_capability_barometric_pressure: "Барометрическое давление",
  weather_capability_visibility: "Видимость",
  
  // Unit toggle
  weather_use_metric_units: "Использовать метрические единицы",
  weather_use_imperial_units: "Использовать имперские единицы",
  
  // Settings related translations
  settings_weather: "Настройки погоды",
  settings_weather_description: "Настройте ваши предпочтения погоды",
  settings_preferred_station: "Предпочитаемая метеостанция",
  settings_unit_system: "Система измерений",
  settings_metric: "Метрическая (°C, км/ч)",
  settings_imperial: "Имперская (°F, миль/ч)",
};
