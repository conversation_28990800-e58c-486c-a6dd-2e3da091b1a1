
export const weatherTranslations = {
  weather_title: "Wetter",
  weather_station: "Wetterstation",
  weather_temperature: "Temperatur",
  weather_water_temp: "Wassertemp.",
  weather_wind_speed: "Wind",
  weather_wind_gust: "Böen",
  weather_wind_direction: "Richtung",
  weather_tide_level: "Gezeiten",
  weather_tide: "Gezeiten", // Add missing translation
  weather_lightning: "Blitz",
  weather_lightning_warning: "Blitzwarnung", // Add missing translation
  weather_search_placeholder: "Nach einer Wetterstation suchen...",
  weather_no_station_selected: "Keine Wetterstation ausgewählt",
  weather_loading: "Wetterdaten werden geladen...",
  weather_error: "Fehler beim Laden der Wetterdaten",
  weather_hourly_forecast: "Stündliche Vorhersage",
  weather_station_saved: "Wetterstation gespeichert",
  weather_unit_saved: "Wettereinheit gespeichert", // Add missing translation
  weather_station_setting: "Wetterstation",
  weather_station_setting_description: "Wählen Sie Ihre bevorzugt<PERSON> Wetterstation",
  weather_unit_setting: "Wettereinheiten",
  weather_unit_metric: "Metrisch (°C, km/h)",
  weather_unit_imperial: "Imperial (°F, mph)",
  weather_not_available: "N/V",
  weather_no_results: "Keine Stationen gefunden",
  weather_no_forecast_available: "Keine zukünftige Vorhersage verfügbar",
  weather_forecast_not_available: "Vorhersagedaten nicht verfügbar",
  weather_now: "Jetzt",

  // Station selector specific translations
  weather_geolocation_not_supported: "Geolokalisierung wird von diesem Browser nicht unterstützt",
  weather_no_stations_found_nearby: "Keine Wetterstationen im Umkreis von 100km gefunden",
  weather_found_nearby_stations: "{count} Wetterstationen in der Nähe gefunden",
  weather_failed_find_nearby: "Fehler beim Finden nahegelegener Stationen",
  weather_location_permission_error: "Standort kann nicht ermittelt werden. Bitte prüfen Sie die Standortberechtigungen.",
  weather_find_nearby_stations: "Nahegelegene Stationen finden",
  weather_nearby_stations: "Nahegelegene Stationen",
  weather_search_results: "Suchergebnisse",
  weather_popular_stations: "Beliebte Stationen",

  // Capability tooltips
  weather_capability_air_temperature: "Lufttemperatur",
  weather_capability_water_temperature: "Wassertemperatur",
  weather_capability_wind_data: "Winddaten",
  weather_capability_water_level: "Wasserstand/Gezeiten",
  weather_capability_barometric_pressure: "Luftdruck",
  weather_capability_visibility: "Sichtweite",

  // Unit toggle
  weather_use_metric_units: "Metrische Einheiten verwenden",
  weather_use_imperial_units: "Imperiale Einheiten verwenden",

  // Settings related translations
  settings_weather: "Wettereinstellungen",
  settings_weather_description: "Konfigurieren Sie Ihre Wettereinstellungen",
  settings_preferred_station: "Bevorzugte Wetterstation",
  settings_unit_system: "Maßsystem",
  settings_metric: "Metrisch (°C, km/h)",
  settings_imperial: "Imperial (°F, mph)",
};
