
import { SupportedLanguage } from "@/types";
import {
  enTranslations,
  esTranslations,
  frTranslations,
  deTranslations,
  koTranslations,
  heTranslations,
  ruTranslations,
  jaTranslations,
  hiTranslations
} from './languages';
import { TranslationsMap, TranslationKey } from './types';

// Combine all language translations into a single translations object
export const translations: Record<SupportedLanguage, Record<string, string>> = {
  en: enTranslations,
  es: esTranslations,
  fr: frTranslations,
  de: deTranslations,
  ko: koTranslations,
  he: heTranslations,
  ru: ruTranslations,
  ja: jaTranslations,
  hi: hiTranslations
};

// Updated translation function with improved type safety
export const getTranslation = (
  language: SupportedLanguage,
  key: TranslationKey
): string => {
  // Check if the language is supported
  if (!translations[language]) {
    console.error(`Language "${language}" is not supported`);
    return key;
  }
  
  // Return the translation if it exists, otherwise return the key itself
  return translations[language][key] || key;
};

// Helper function to check if all translations have the same keys
export function validateTranslations(): { valid: boolean, missingKeys: Record<string, string[]> } {
  const result = { valid: true, missingKeys: {} as Record<string, string[]> };
  
  // Get all keys from English translations as the reference
  const referenceKeys = Object.keys(translations.en) as TranslationKey[];
  
  // Check each language
  Object.entries(translations).forEach(([lang, trans]) => {
    if (lang === 'en') return; // Skip English as it's our reference
    
    const langKeys = Object.keys(trans);
    const missing = referenceKeys.filter(
      (key) => !Object.prototype.hasOwnProperty.call(trans, key)
    ) as TranslationKey[];
    
    if (missing.length > 0) {
      result.valid = false;
      result.missingKeys[lang] = missing;
    }
  });
  
  return result;
}

/**
 * Check if a key exists in all language translations
 */
export function isKeyInAllTranslations(key: TranslationKey): boolean {
  return Object.values(translations).every(
    (langTranslations) => Object.prototype.hasOwnProperty.call(langTranslations, key)
  );
}

/**
 * Get all translation keys that are in at least one language
 */
export function getAllTranslationKeys(): TranslationKey[] {
  const keysSet = new Set<string>();
  
  Object.values(translations).forEach((langTranslations) => {
    Object.keys(langTranslations).forEach((key) => {
      keysSet.add(key);
    });
  });
  
  return Array.from(keysSet) as TranslationKey[];
}
