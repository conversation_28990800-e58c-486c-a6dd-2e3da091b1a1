
/**
 * Translation System - Optimized with Backward Compatibility
 *
 * This file now uses the optimized translation system while maintaining
 * backward compatibility with existing code.
 */

import { SupportedLanguage } from "@/types";
import { TranslationsMap, TranslationKey } from './types';

// Import optimized system
import {
  getTranslation as getOptimizedTranslation,
  translationRegistry
} from './optimized/translationRegistry';
import {
  translations as legacyTranslations,
  getTranslation as legacyGetTranslation,
  validateTranslations as legacyValidateTranslations,
  isKeyInAllTranslations as legacyIsKeyInAllTranslations,
  getAllTranslationKeys as legacyGetAllTranslationKeys
} from './optimized/compatibilityLayer';

// Export legacy translations object for backward compatibility
export const translations = legacyTranslations;

// Export optimized translation function (recommended for new code)
export const getTranslation = getOptimizedTranslation;

// Export legacy functions for backward compatibility
export const validateTranslations = legacyValidateTranslations;
export const isKeyInAllTranslations = legacyIsKeyInAllTranslations;
export const getAllTranslationKeys = legacyGetAllTranslationKeys;

// Export optimized system for new code
export {
  translationRegistry,
  getTranslation as getOptimizedTranslation,
  validateTranslations as validateOptimizedTranslations,
  getTranslationCompleteness
} from './optimized/translationRegistry';

export {
  useOptimizedTranslation,
  useTranslationValidation
} from './optimized/useOptimizedTranslation';

export {
  loadLanguageTranslations,
  preloadLanguages,
  initializeTranslations,
  getCacheStats
} from './optimized/languageLoader';
