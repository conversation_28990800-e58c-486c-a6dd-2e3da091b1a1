
export type WeatherTranslationKey =
  | 'weather_title'
  | 'weather_station'
  | 'weather_temperature'
  | 'weather_water_temp'
  | 'weather_wind_speed'
  | 'weather_wind_gust'
  | 'weather_wind_direction'
  | 'weather_tide_level'
  | 'weather_tide'
  | 'weather_lightning'
  | 'weather_lightning_warning'
  | 'weather_search_placeholder'
  | 'weather_no_station_selected'
  | 'weather_loading'
  | 'weather_error'
  | 'weather_hourly_forecast'
  | 'weather_station_saved'
  | 'weather_unit_saved'
  | 'weather_station_setting'
  | 'weather_station_setting_description'
  | 'weather_unit_setting'
  | 'weather_unit_metric'
  | 'weather_unit_imperial'
  | 'weather_not_available'
  | 'weather_no_results'
  | 'weather_forecast_not_available'
  // Station selector specific keys
  | 'weather_geolocation_not_supported'
  | 'weather_no_stations_found_nearby'
  | 'weather_found_nearby_stations'
  | 'weather_failed_find_nearby'
  | 'weather_location_permission_error'
  | 'weather_find_nearby_stations'
  | 'weather_nearby_stations'
  | 'weather_search_results'
  | 'weather_popular_stations'
  // Capability tooltips
  | 'weather_capability_air_temperature'
  | 'weather_capability_water_temperature'
  | 'weather_capability_wind_data'
  | 'weather_capability_water_level'
  | 'weather_capability_barometric_pressure'
  | 'weather_capability_visibility'
  // Unit toggle
  | 'weather_use_metric_units'
  | 'weather_use_imperial_units'
  | 'weather_forecast_not_available'  // Add missing key
  | 'weather_now'
  | 'settings_weather'
  | 'settings_weather_description'
  | 'settings_preferred_station'
  | 'settings_unit_system'
  | 'settings_metric'
  | 'settings_imperial';
