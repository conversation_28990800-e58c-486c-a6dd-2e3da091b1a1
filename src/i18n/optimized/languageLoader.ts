/**
 * Optimized Language Loader
 * 
 * This module provides dynamic loading of translation files with caching,
 * lazy loading, and performance optimization.
 */

import { SupportedLanguage } from '@/types/language';
import { LanguageTranslationSet, translationRegistry } from './translationRegistry';

// Cache for loaded translations
const translationCache = new Map<SupportedLanguage, Record<string, string>>();

// Track loading states
const loadingStates = new Map<SupportedLanguage, Promise<Record<string, string>>>();

/**
 * Dynamically import translation files
 */
const importTranslations = async (language: SupportedLanguage): Promise<Record<string, string>> => {
  try {
    switch (language) {
      case 'en':
        const enModule = await import('../categories/en');
        return enModule.enTranslations;
      
      case 'es':
        const esModule = await import('../categories/es');
        return esModule.esTranslations;
      
      case 'fr':
        const frModule = await import('../categories/fr');
        return frModule.frTranslations;
      
      case 'de':
        const deModule = await import('../categories/de');
        return deModule.deTranslations;
      
      case 'ko':
        const koModule = await import('../categories/ko');
        return koModule.koTranslations;
      
      case 'he':
        const heModule = await import('../categories/he');
        return heModule.heTranslations;
      
      case 'ru':
        const ruModule = await import('../categories/ru');
        return ruModule.ruTranslations;
      
      case 'ja':
        const jaModule = await import('../categories/ja');
        return jaModule.jaTranslations;
      
      case 'hi':
        const hiModule = await import('../categories/hi');
        return hiModule.hiTranslations;
      
      default:
        console.warn(`Unsupported language: ${language}`);
        const fallbackModule = await import('../categories/en');
        return fallbackModule.enTranslations;
    }
  } catch (error) {
    console.error(`Failed to load translations for ${language}:`, error);
    // Fallback to English
    const fallbackModule = await import('../categories/en');
    return fallbackModule.enTranslations;
  }
};

/**
 * Load translations for a specific language with caching
 */
export const loadLanguageTranslations = async (language: SupportedLanguage): Promise<Record<string, string>> => {
  // Return cached translations if available
  if (translationCache.has(language)) {
    return translationCache.get(language)!;
  }

  // Return existing loading promise if already loading
  if (loadingStates.has(language)) {
    return loadingStates.get(language)!;
  }

  // Start loading
  const loadingPromise = importTranslations(language);
  loadingStates.set(language, loadingPromise);

  try {
    const translations = await loadingPromise;
    
    // Cache the translations
    translationCache.set(language, translations);
    
    // Register with the translation registry
    const languageSet: LanguageTranslationSet = {
      language,
      translations,
      completeness: calculateCompleteness(translations, language),
      lastUpdated: new Date(),
    };
    translationRegistry.register(languageSet);
    
    return translations;
  } finally {
    // Clean up loading state
    loadingStates.delete(language);
  }
};

/**
 * Calculate translation completeness compared to English
 */
const calculateCompleteness = (translations: Record<string, string>, language: SupportedLanguage): number => {
  if (language === 'en') return 100;
  
  // Get English translations for comparison
  const englishTranslations = translationCache.get('en');
  if (!englishTranslations) return 0;
  
  const englishKeys = Object.keys(englishTranslations);
  const translationKeys = Object.keys(translations);
  const matchingKeys = translationKeys.filter(key => englishKeys.includes(key));
  
  return englishKeys.length > 0 ? Math.round((matchingKeys.length / englishKeys.length) * 100) : 0;
};

/**
 * Preload multiple languages
 */
export const preloadLanguages = async (languages: SupportedLanguage[]): Promise<void> => {
  const loadPromises = languages.map(lang => loadLanguageTranslations(lang));
  await Promise.all(loadPromises);
};

/**
 * Get cached translation or load if not available
 */
export const getOrLoadTranslations = async (language: SupportedLanguage): Promise<Record<string, string>> => {
  if (translationCache.has(language)) {
    return translationCache.get(language)!;
  }
  
  return loadLanguageTranslations(language);
};

/**
 * Clear translation cache
 */
export const clearTranslationCache = (language?: SupportedLanguage): void => {
  if (language) {
    translationCache.delete(language);
  } else {
    translationCache.clear();
  }
};

/**
 * Get cache statistics
 */
export const getCacheStats = () => {
  return {
    cachedLanguages: Array.from(translationCache.keys()),
    cacheSize: translationCache.size,
    loadingLanguages: Array.from(loadingStates.keys()),
  };
};

/**
 * Initialize the translation system with default language
 */
export const initializeTranslations = async (defaultLanguage: SupportedLanguage = 'en'): Promise<void> => {
  try {
    // Always load English first as the reference
    await loadLanguageTranslations('en');
    
    // Load the default language if it's not English
    if (defaultLanguage !== 'en') {
      await loadLanguageTranslations(defaultLanguage);
    }
    
    console.log('Translation system initialized successfully');
  } catch (error) {
    console.error('Failed to initialize translation system:', error);
  }
};
