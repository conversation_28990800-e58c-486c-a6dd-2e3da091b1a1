/**
 * Optimized Translation Registry
 * 
 * This file provides a centralized, optimized approach to managing translations
 * with better performance, consistency checking, and maintainability.
 */

import { SupportedLanguage } from '@/types/language';
import { TranslationKey } from '../types';

// Define the structure for a complete language translation set
export interface LanguageTranslationSet {
  language: SupportedLanguage;
  translations: Record<string, string>;
  completeness: number; // Percentage of translations completed (0-100)
  lastUpdated: Date;
}

// Registry to track all language translation sets
class TranslationRegistry {
  private registry = new Map<SupportedLanguage, LanguageTranslationSet>();
  private referenceLanguage: SupportedLanguage = 'en';
  private referenceKeys: Set<string> = new Set();

  /**
   * Register a language translation set
   */
  register(languageSet: LanguageTranslationSet): void {
    this.registry.set(languageSet.language, languageSet);
    
    // Update reference keys if this is the reference language
    if (languageSet.language === this.referenceLanguage) {
      this.referenceKeys = new Set(Object.keys(languageSet.translations));
    }
  }

  /**
   * Get translations for a specific language
   */
  getTranslations(language: SupportedLanguage): Record<string, string> {
    const languageSet = this.registry.get(language);
    if (!languageSet) {
      console.warn(`Language ${language} not found in registry`);
      return this.registry.get(this.referenceLanguage)?.translations || {};
    }
    return languageSet.translations;
  }

  /**
   * Get a specific translation
   */
  getTranslation(language: SupportedLanguage, key: TranslationKey): string {
    const translations = this.getTranslations(language);
    return translations[key] || key;
  }

  /**
   * Check translation completeness for all languages
   */
  getCompletenessReport(): Record<SupportedLanguage, {
    completeness: number;
    missingKeys: string[];
    extraKeys: string[];
  }> {
    const report: Record<string, any> = {};

    this.registry.forEach((languageSet, language) => {
      const translationKeys = new Set(Object.keys(languageSet.translations));
      const missingKeys = Array.from(this.referenceKeys).filter(key => !translationKeys.has(key));
      const extraKeys = Array.from(translationKeys).filter(key => !this.referenceKeys.has(key));
      
      const completeness = this.referenceKeys.size > 0 
        ? Math.round(((this.referenceKeys.size - missingKeys.length) / this.referenceKeys.size) * 100)
        : 100;

      report[language] = {
        completeness,
        missingKeys,
        extraKeys,
      };
    });

    return report;
  }

  /**
   * Get all supported languages
   */
  getSupportedLanguages(): SupportedLanguage[] {
    return Array.from(this.registry.keys());
  }

  /**
   * Get languages that need attention (incomplete translations)
   */
  getIncompleteLanguages(threshold: number = 90): SupportedLanguage[] {
    const report = this.getCompletenessReport();
    return Object.entries(report)
      .filter(([_, data]) => data.completeness < threshold)
      .map(([language]) => language as SupportedLanguage);
  }

  /**
   * Validate all translations
   */
  validate(): {
    isValid: boolean;
    issues: string[];
    summary: Record<SupportedLanguage, number>;
  } {
    const issues: string[] = [];
    const summary: Record<string, number> = {};
    const report = this.getCompletenessReport();

    Object.entries(report).forEach(([language, data]) => {
      summary[language] = data.completeness;
      
      if (data.missingKeys.length > 0) {
        issues.push(`${language}: Missing ${data.missingKeys.length} keys`);
      }
      
      if (data.extraKeys.length > 0) {
        issues.push(`${language}: Has ${data.extraKeys.length} extra keys`);
      }
    });

    return {
      isValid: issues.length === 0,
      issues,
      summary: summary as Record<SupportedLanguage, number>,
    };
  }

  /**
   * Get fallback chain for a language
   */
  getFallbackChain(language: SupportedLanguage): SupportedLanguage[] {
    const fallbacks: Record<SupportedLanguage, SupportedLanguage[]> = {
      'en': ['en'],
      'es': ['es', 'en'],
      'fr': ['fr', 'en'],
      'de': ['de', 'en'],
      'ko': ['ko', 'en'],
      'he': ['he', 'en'],
      'ru': ['ru', 'en'],
      'ja': ['ja', 'en'],
      'hi': ['hi', 'en'],
    };

    return fallbacks[language] || ['en'];
  }

  /**
   * Get translation with fallback support
   */
  getTranslationWithFallback(language: SupportedLanguage, key: TranslationKey): string {
    const fallbackChain = this.getFallbackChain(language);
    
    for (const fallbackLang of fallbackChain) {
      const translations = this.getTranslations(fallbackLang);
      if (translations[key]) {
        return translations[key];
      }
    }
    
    return key; // Return key as last resort
  }
}

// Create and export the singleton registry
export const translationRegistry = new TranslationRegistry();

// Export utility functions
export const registerLanguage = (languageSet: LanguageTranslationSet) => {
  translationRegistry.register(languageSet);
};

export const getTranslation = (language: SupportedLanguage, key: TranslationKey): string => {
  return translationRegistry.getTranslationWithFallback(language, key);
};

export const validateTranslations = () => {
  return translationRegistry.validate();
};

export const getTranslationCompleteness = () => {
  return translationRegistry.getCompletenessReport();
};
