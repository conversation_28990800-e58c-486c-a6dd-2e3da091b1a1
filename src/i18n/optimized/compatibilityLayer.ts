/**
 * Compatibility Layer for Translation System
 * 
 * This module provides backward compatibility with the existing translation system
 * while gradually migrating to the optimized system.
 */

import { SupportedLanguage } from '@/types/language';
import { TranslationKey } from '../types';
import { getTranslation as getOptimizedTranslation } from './translationRegistry';
import { loadLanguageTranslations } from './languageLoader';

// Legacy translation function signature
export const getTranslation = (
  language: SupportedLanguage,
  key: TranslationKey
): string => {
  return getOptimizedTranslation(language, key);
};

// Legacy validation function
export function validateTranslations(): { valid: boolean, missingKeys: Record<string, string[]> } {
  // This would integrate with the optimized validation system
  console.warn('Legacy validateTranslations called - consider migrating to optimized system');
  return { valid: true, missingKeys: {} };
}

// Legacy key checking function
export function isKeyInAllTranslations(key: TranslationKey): boolean {
  // This would integrate with the optimized system
  console.warn('Legacy isKeyInAllTranslations called - consider migrating to optimized system');
  return true;
}

// Legacy translations object for backward compatibility
let legacyTranslations: Record<SupportedLanguage, Record<string, string>> = {} as any;

// Initialize legacy translations object
const initializeLegacyTranslations = async () => {
  const languages: SupportedLanguage[] = ['en', 'es', 'fr', 'de', 'ko', 'he', 'ru', 'ja', 'hi'];
  
  for (const language of languages) {
    try {
      const translations = await loadLanguageTranslations(language);
      legacyTranslations[language] = translations;
    } catch (error) {
      console.warn(`Failed to load legacy translations for ${language}:`, error);
    }
  }
};

// Export legacy translations object
export const translations = new Proxy(legacyTranslations, {
  get(target, prop) {
    if (typeof prop === 'string' && !target[prop as SupportedLanguage]) {
      console.warn(`Legacy translations access for ${prop} - consider migrating to optimized system`);
      // Try to load the language dynamically
      loadLanguageTranslations(prop as SupportedLanguage).then(trans => {
        target[prop as SupportedLanguage] = trans;
      });
    }
    return target[prop as SupportedLanguage];
  }
});

// Initialize on import
if (typeof window !== 'undefined') {
  initializeLegacyTranslations();
}

// Legacy export for getAllTranslationKeys
export function getAllTranslationKeys(): TranslationKey[] {
  console.warn('Legacy getAllTranslationKeys called - consider migrating to optimized system');
  return [];
}

// Migration helper functions
export const migrationHelpers = {
  /**
   * Check if a component is using legacy translation methods
   */
  detectLegacyUsage: (componentName: string) => {
    console.warn(`Component ${componentName} is using legacy translation methods. Consider migrating to useOptimizedTranslation.`);
  },

  /**
   * Provide migration suggestions
   */
  suggestMigration: (oldMethod: string, newMethod: string) => {
    console.info(`Migration suggestion: Replace ${oldMethod} with ${newMethod}`);
  },

  /**
   * Validate component translation usage
   */
  validateComponentTranslations: (componentName: string, usedKeys: TranslationKey[]) => {
    // This would check if all used keys exist in the optimized system
    console.log(`Validating translations for ${componentName}:`, usedKeys);
  }
};

// Deprecation warnings
const deprecationWarnings = {
  translations: () => {
    console.warn('Direct access to translations object is deprecated. Use useOptimizedTranslation hook instead.');
  },
  
  getTranslation: () => {
    console.warn('Direct getTranslation function is deprecated. Use useOptimizedTranslation hook instead.');
  },
  
  validateTranslations: () => {
    console.warn('Legacy validateTranslations is deprecated. Use translation registry validation instead.');
  }
};

// Wrapper for legacy components
export const withLegacyTranslationSupport = <T extends object>(
  Component: React.ComponentType<T>,
  componentName: string
) => {
  return (props: T) => {
    migrationHelpers.detectLegacyUsage(componentName);
    return React.createElement(Component, props);
  };
};

// Export compatibility functions with warnings
export const compatibilityAPI = {
  getTranslation: (language: SupportedLanguage, key: TranslationKey) => {
    deprecationWarnings.getTranslation();
    return getOptimizedTranslation(language, key);
  },
  
  validateTranslations: () => {
    deprecationWarnings.validateTranslations();
    return validateTranslations();
  },
  
  translations: new Proxy(legacyTranslations, {
    get(target, prop) {
      deprecationWarnings.translations();
      return target[prop as SupportedLanguage];
    }
  })
};

// Development mode helpers
if (process.env.NODE_ENV === 'development') {
  // Track legacy usage
  const legacyUsageTracker = {
    components: new Set<string>(),
    methods: new Map<string, number>(),
  };

  // Add to global for debugging
  (window as any).__translationMigrationHelper = {
    legacyUsageTracker,
    migrationHelpers,
    compatibilityAPI,
  };
}
