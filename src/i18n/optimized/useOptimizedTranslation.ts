/**
 * Optimized Translation Hook
 * 
 * This hook provides an optimized translation function with caching,
 * fallback support, and performance monitoring.
 */

import { useCallback, useEffect, useState } from 'react';
import { SupportedLanguage } from '@/types/language';
import { TranslationKey } from '../types';
import { getTranslation } from './translationRegistry';
import { loadLanguageTranslations, getOrLoadTranslations } from './languageLoader';

interface UseOptimizedTranslationOptions {
  fallbackLanguage?: SupportedLanguage;
  enablePerformanceMonitoring?: boolean;
}

interface TranslationPerformanceMetrics {
  totalTranslations: number;
  averageTime: number;
  cacheHitRate: number;
  fallbackUsage: number;
}

export const useOptimizedTranslation = (
  language: SupportedLanguage,
  options: UseOptimizedTranslationOptions = {}
) => {
  const {
    fallbackLanguage = 'en',
    enablePerformanceMonitoring = false,
  } = options;

  const [isLoading, setIsLoading] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [metrics, setMetrics] = useState<TranslationPerformanceMetrics>({
    totalTranslations: 0,
    averageTime: 0,
    cacheHitRate: 0,
    fallbackUsage: 0,
  });

  // Performance tracking
  const performanceData = {
    translationTimes: [] as number[],
    cacheHits: 0,
    cacheMisses: 0,
    fallbackUsed: 0,
  };

  // Load translations when language changes
  useEffect(() => {
    const loadTranslations = async () => {
      setIsLoading(true);
      try {
        await getOrLoadTranslations(language);
        setIsReady(true);
      } catch (error) {
        console.error(`Failed to load translations for ${language}:`, error);
        // Try to load fallback language
        if (language !== fallbackLanguage) {
          await getOrLoadTranslations(fallbackLanguage);
        }
        setIsReady(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadTranslations();
  }, [language, fallbackLanguage]);

  // Optimized translation function
  const t = useCallback((key: TranslationKey, params?: Record<string, string | number>): string => {
    const startTime = enablePerformanceMonitoring ? performance.now() : 0;

    try {
      let translation = getTranslation(language, key);
      
      // Track if fallback was used
      if (enablePerformanceMonitoring && translation === key && language !== fallbackLanguage) {
        performanceData.fallbackUsed++;
        translation = getTranslation(fallbackLanguage, key);
      }

      // Handle parameter substitution
      if (params && translation !== key) {
        translation = Object.entries(params).reduce((str, [paramKey, value]) => {
          return str.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(value));
        }, translation);
      }

      // Performance tracking
      if (enablePerformanceMonitoring) {
        const endTime = performance.now();
        performanceData.translationTimes.push(endTime - startTime);
        
        // Update metrics periodically
        if (performanceData.translationTimes.length % 100 === 0) {
          updateMetrics();
        }
      }

      return translation;
    } catch (error) {
      console.error(`Translation error for key "${key}":`, error);
      return key;
    }
  }, [language, fallbackLanguage, enablePerformanceMonitoring]);

  // Update performance metrics
  const updateMetrics = useCallback(() => {
    const times = performanceData.translationTimes;
    const totalHits = performanceData.cacheHits + performanceData.cacheMisses;
    
    setMetrics({
      totalTranslations: times.length,
      averageTime: times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0,
      cacheHitRate: totalHits > 0 ? (performanceData.cacheHits / totalHits) * 100 : 0,
      fallbackUsage: times.length > 0 ? (performanceData.fallbackUsed / times.length) * 100 : 0,
    });
  }, []);

  // Batch translation function for multiple keys
  const tBatch = useCallback((keys: TranslationKey[]): Record<string, string> => {
    const result: Record<string, string> = {};
    keys.forEach(key => {
      result[key] = t(key);
    });
    return result;
  }, [t]);

  // Check if a translation exists
  const hasTranslation = useCallback((key: TranslationKey): boolean => {
    const translation = getTranslation(language, key);
    return translation !== key;
  }, [language]);

  // Get translation with explicit fallback
  const tWithFallback = useCallback((
    key: TranslationKey, 
    fallback: string,
    params?: Record<string, string | number>
  ): string => {
    const translation = t(key, params);
    return translation === key ? fallback : translation;
  }, [t]);

  // Preload additional languages
  const preloadLanguage = useCallback(async (lang: SupportedLanguage): Promise<void> => {
    try {
      await loadLanguageTranslations(lang);
    } catch (error) {
      console.error(`Failed to preload language ${lang}:`, error);
    }
  }, []);

  return {
    t,
    tBatch,
    tWithFallback,
    hasTranslation,
    preloadLanguage,
    isLoading,
    isReady,
    metrics: enablePerformanceMonitoring ? metrics : null,
    language,
  };
};

// Hook for translation validation during development
export const useTranslationValidation = (language: SupportedLanguage) => {
  const [validationResults, setValidationResults] = useState<{
    missingKeys: string[];
    extraKeys: string[];
    completeness: number;
  } | null>(null);

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // This would integrate with the translation registry validation
      // Implementation would depend on having access to the registry
      console.log(`Validation for ${language} would run here`);
    }
  }, [language]);

  return validationResults;
};
