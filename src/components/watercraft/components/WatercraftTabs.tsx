import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Watercraft, Boat } from '@/types';
import { TranslationKey } from '@/i18n/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIsMobile } from '@/hooks/use-mobile';
import { WatercraftGrid } from './WatercraftGrid';

interface WatercraftTabsProps {
  watercrafts: (Watercraft | Boat)[];
  groupedWatercrafts: Record<string, (Watercraft | Boat)[]>;
  onToggleFavorite: (watercraftId: string) => void;
  onCheckout: (watercraft: Watercraft | Boat) => void;
  onViewDetails: (watercraft: Watercraft | Boat) => void;
}

export const WatercraftTabs: React.FC<WatercraftTabsProps> = ({
  watercrafts,
  groupedWatercrafts,
  onToggleFavorite,
  onCheckout,
  onViewDetails,
}) => {
  const { t } = useLanguage();
  const isMobile = useIsMobile();

  // Helper function to convert watercraft type to translation key
  const typeToTranslationKey = (type: string): TranslationKey => {
    return type as TranslationKey;
  };

  const renderTabsList = () => {
    const tabTriggers = (
      <>
        <TabsTrigger value="all">{t('all') || 'All'}</TabsTrigger>
        {Object.keys(groupedWatercrafts).map(type => (
          <TabsTrigger key={type} value={type}>
            {t(typeToTranslationKey(type))} ({groupedWatercrafts[type].length})
          </TabsTrigger>
        ))}
      </>
    );

    if (isMobile) {
      return (
        <ScrollArea className="w-full pb-2">
          <TabsList className="inline-flex w-auto mb-2">
            {tabTriggers}
          </TabsList>
        </ScrollArea>
      );
    }

    return <TabsList>{tabTriggers}</TabsList>;
  };

  return (
    <Tabs defaultValue="all">
      {renderTabsList()}
      
      <TabsContent value="all" className="pt-4">
        <WatercraftGrid
          watercrafts={watercrafts}
          onToggleFavorite={onToggleFavorite}
          onCheckout={onCheckout}
          onViewDetails={onViewDetails}
        />
      </TabsContent>
      
      {Object.keys(groupedWatercrafts).map(type => (
        <TabsContent key={type} value={type} className="pt-4">
          <WatercraftGrid
            watercrafts={groupedWatercrafts[type]}
            onToggleFavorite={onToggleFavorite}
            onCheckout={onCheckout}
            onViewDetails={onViewDetails}
          />
        </TabsContent>
      ))}
    </Tabs>
  );
};
