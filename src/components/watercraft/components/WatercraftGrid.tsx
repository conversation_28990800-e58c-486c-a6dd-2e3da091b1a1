import React from 'react';
import { Watercraft, Boat } from '@/types';
import WatercraftCard from '../WatercraftCard';

interface WatercraftGridProps {
  watercrafts: (Watercraft | Boat)[];
  onToggleFavorite: (watercraftId: string) => void;
  onCheckout: (watercraft: Watercraft | Boat) => void;
  onViewDetails: (watercraft: Watercraft | Boat) => void;
}

export const WatercraftGrid: React.FC<WatercraftGridProps> = ({
  watercrafts,
  onToggleFavorite,
  onCheckout,
  onViewDetails,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {watercrafts.map(watercraft => (
        <WatercraftCard
          key={watercraft.id}
          watercraft={watercraft}
          onToggleFavorite={() => onToggleFavorite(watercraft.id)}
          onScheduleJaunt={() => onCheckout(watercraft)}
          onScheduleMaintenance={() => console.log('Maintenance scheduled for', watercraft.id)}
          onViewDetails={() => onViewDetails(watercraft)}
        />
      ))}
    </div>
  );
};
