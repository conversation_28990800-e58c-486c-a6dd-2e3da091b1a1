import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Loader2 } from 'lucide-react';

interface WatercraftSkeletonProps {
  count?: number;
}

export const WatercraftSkeleton: React.FC<WatercraftSkeletonProps> = ({ count = 8 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-card rounded-lg shadow-sm border p-4 h-64 animate-pulse">
          <div className="h-5 bg-muted rounded w-3/4 mb-4"></div>
          <div className="grid grid-cols-2 gap-2 mb-4">
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded"></div>
          </div>
          <div className="h-24 bg-muted rounded mb-4"></div>
          <div className="flex justify-between">
            <div className="h-8 bg-muted rounded w-20"></div>
            <div className="h-8 bg-muted rounded w-20"></div>
          </div>
        </div>
      ))}
    </div>
  );
};

interface WatercraftLoadingStateProps {
  onFilterChange: (filters: any) => void;
}

export const WatercraftLoadingState: React.FC<WatercraftLoadingStateProps> = ({ onFilterChange }) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-4">
      <div className="text-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
        <p className="text-muted-foreground">{t('loading_watercrafts')}</p>
      </div>
      
      <WatercraftSkeleton />
    </div>
  );
};
