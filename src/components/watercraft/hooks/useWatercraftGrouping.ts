import { useMemo } from 'react';
import { Watercraft, Boat } from '@/types';

export const useWatercraftGrouping = (watercrafts: (Watercraft | Boat)[]) => {
  const groupedWatercrafts = useMemo(() => {
    return watercrafts.reduce((acc, craft) => {
      if (!acc[craft.type]) {
        acc[craft.type] = [];
      }
      acc[craft.type].push(craft);
      return acc;
    }, {} as Record<string, (Watercraft | Boat)[]>);
  }, [watercrafts]);

  return { groupedWatercrafts };
};
