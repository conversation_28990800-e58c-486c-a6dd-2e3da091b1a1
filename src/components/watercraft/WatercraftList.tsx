
import { useState, useEffect } from 'react';
import { Watercraft, Boat, WatercraftFilters } from '@/types';
import { WatercraftFilters as FiltersComponent } from './WatercraftFilters';
import { WatercraftLoadingState } from './components/WatercraftSkeleton';
import { WatercraftEmptyState } from './components/WatercraftEmptyState';
import { WatercraftTabs } from './components/WatercraftTabs';
import { useWatercraftGrouping } from './hooks/useWatercraftGrouping';

interface WatercraftListProps {
  watercrafts: (Watercraft | Boat)[];
  onFilterChange: (filters: WatercraftFilters) => void;
  onToggleFavorite: (watercraftId: string) => void;
  onCheckout: (watercraft: Watercraft | Boat) => void;
  onViewDetails: (watercraft: Watercraft | Boat) => void;
}

export function WatercraftList({
  watercrafts,
  onFilterChange,
  onToggleFavorite,
  onCheckout,
  onViewDetails
}: WatercraftListProps) {
  const [isLoading, setIsLoading] = useState(true);
  const { groupedWatercrafts } = useWatercraftGrouping(watercrafts);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <FiltersComponent onFilterChange={onFilterChange} />
        <WatercraftLoadingState onFilterChange={onFilterChange} />
      </div>
    );
  }
  return (
    <div className="space-y-4">
      <FiltersComponent onFilterChange={onFilterChange} />

      {watercrafts.length === 0 ? (
        <WatercraftEmptyState />
      ) : (
        <WatercraftTabs
          watercrafts={watercrafts}
          groupedWatercrafts={groupedWatercrafts}
          onToggleFavorite={onToggleFavorite}
          onCheckout={onCheckout}
          onViewDetails={onViewDetails}
        />
      )}
    </div>
  );
}
