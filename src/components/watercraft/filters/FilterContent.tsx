import React from 'react';
import { WatercraftFilters } from '@/types';
import { TypeFilter } from './TypeFilter';
import { BoatTypeFilter } from './BoatTypeFilter';
import { StatusFilter } from './StatusFilter';
import { SkillLevelFilter } from './SkillLevelFilter';
import { WeightRangeFilter } from './WeightRangeFilter';
import { FavoritesToggle } from './FavoritesToggle';
import { FilterPopoverHeader } from './FilterPopoverHeader';
import { useFilterOptions } from './hooks/useFilterOptions';
import { useFilterHandlers } from './hooks/useFilterHandlers';

interface FilterContentProps {
  filters: WatercraftFilters;
  onFilterChange: (filters: WatercraftFilters) => void;
  onResetFilters: () => void;
}

export const FilterContent: React.FC<FilterContentProps> = ({
  filters,
  onFilterChange,
  onResetFilters,
}) => {
  const {
    watercraftTypes,
    boatTypes,
    statusOptions,
    shouldShowBoatTypeFilter,
    shouldShowWeightRangeFilter,
  } = useFilterOptions({ filters });

  const {
    handleTypeToggle,
    handleBoatTypeToggle,
    handleStatusToggle,
    handleSkillLevelChange,
    handleWeightRangeChange,
    handleFavoritesToggle,
  } = useFilterHandlers({ filters, onFilterChange });

  return (
    <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-1 pb-1">
      {/* Filter Header */}
      <FilterPopoverHeader onResetFilters={onResetFilters} />
      
      {/* Type Filter */}
      <TypeFilter 
        types={watercraftTypes} 
        selectedTypes={filters.type} 
        onChange={handleTypeToggle} 
      />
      
      {/* Boat Type Filter (only show if 'boat' is selected) */}
      {shouldShowBoatTypeFilter && (
        <BoatTypeFilter 
          boatTypes={boatTypes} 
          selectedBoatTypes={filters.boatTypes || []} 
          onChange={handleBoatTypeToggle} 
        />
      )}
      
      {/* Status Filter */}
      <StatusFilter 
        statusOptions={statusOptions} 
        selectedStatus={filters.status} 
        onChange={handleStatusToggle} 
      />
      
      {/* Skill Level Slider */}
      <SkillLevelFilter
        value={filters.skillLevel || [1, 5]}
        onChange={handleSkillLevelChange}
      />
      
      {/* Weight Range Slider (only for boats) */}
      {shouldShowWeightRangeFilter && (
        <WeightRangeFilter
          value={filters.weightRange || [50, 100]}
          onChange={handleWeightRangeChange}
        />
      )}
      
      {/* Favorites Toggle */}
      <FavoritesToggle 
        showFavoritesOnly={filters.showFavoritesOnly} 
        onToggle={handleFavoritesToggle} 
      />
    </div>
  );
};
