
import { useState } from 'react';
import { WatercraftFilters } from '@/types';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { useIsMobile } from '@/hooks/use-mobile';
import { FilterTriggerButton } from './FilterTriggerButton';
import { FilterContent } from './FilterContent';
import { useFilterOptions } from './hooks/useFilterOptions';

interface FilterPopoverProps {
  filters: WatercraftFilters;
  onFilterChange: (filters: WatercraftFilters) => void;
  onResetFilters: () => void;
}

export function FilterPopover({ filters, onFilterChange, onResetFilters }: FilterPopoverProps) {
  const [showFilters, setShowFilters] = useState(false);
  const isMobile = useIsMobile();

  const { activeFilterCount } = useFilterOptions({ filters });
  
  return (
    <Popover open={showFilters} onOpenChange={setShowFilters}>
      <PopoverTrigger asChild>
        <FilterTriggerButton activeFilterCount={activeFilterCount} />
      </PopoverTrigger>
      <PopoverContent
        side={isMobile ? "bottom" : "right"}
        align={isMobile ? "center" : "start"}
        className="w-[calc(100vw-2rem)] sm:w-96 max-w-md"
        avoidCollisions={true}
        sticky="always"
        sideOffset={5}
      >
        <FilterContent
          filters={filters}
          onFilterChange={onFilterChange}
          onResetFilters={onResetFilters}
        />
      </PopoverContent>
    </Popover>
  );
}
