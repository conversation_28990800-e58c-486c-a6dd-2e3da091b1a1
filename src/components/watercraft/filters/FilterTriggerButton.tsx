import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { Filter } from 'lucide-react';

interface FilterTriggerButtonProps {
  activeFilterCount: number;
  onClick?: () => void;
}

export const FilterTriggerButton: React.FC<FilterTriggerButtonProps> = ({ 
  activeFilterCount, 
  onClick 
}) => {
  const { t } = useLanguage();
  
  return (
    <Button 
      variant="outline" 
      className="flex gap-2 w-full sm:w-auto justify-center"
      onClick={onClick}
    >
      <Filter className="h-4 w-4" />
      <span className="whitespace-nowrap overflow-hidden text-ellipsis">
        {t('filters') || 'Filters'}
      </span>
      {activeFilterCount > 0 && (
        <span className="inline-flex items-center justify-center rounded-full bg-primary w-5 h-5 text-[10px] text-white font-medium">
          {activeFilterCount}
        </span>
      )}
    </Button>
  );
};
