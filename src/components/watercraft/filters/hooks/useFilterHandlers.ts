import { WatercraftFilters, WatercraftStatus } from '@/types';

interface UseFilterHandlersProps {
  filters: WatercraftFilters;
  onFilterChange: (filters: WatercraftFilters) => void;
}

export const useFilterHandlers = ({ filters, onFilterChange }: UseFilterHandlersProps) => {
  const handleTypeToggle = (types: string[]) => {
    onFilterChange({ ...filters, type: types });
  };
  
  const handleBoatTypeToggle = (types: string[]) => {
    onFilterChange({ ...filters, boatTypes: types });
  };
  
  const handleStatusToggle = (status: string[]) => {
    onFilterChange({ ...filters, status: status as WatercraftStatus[] });
  };
  
  const handleSkillLevelChange = (value: number[]) => {
    onFilterChange({ ...filters, skillLevel: [value[0], value[1]] as [number, number] });
  };
  
  const handleWeightRangeChange = (value: number[]) => {
    onFilterChange({ ...filters, weightRange: [value[0], value[1]] as [number, number] });
  };
  
  const handleFavoritesToggle = () => {
    onFilterChange({ ...filters, showFavoritesOnly: !filters.showFavoritesOnly });
  };

  return {
    handleTypeToggle,
    handleBoatTypeToggle,
    handleStatusToggle,
    handleSkillLevelChange,
    handleWeightRangeChange,
    handleFavoritesToggle,
  };
};
