import { WatercraftType, BoatType, WatercraftStatus, WatercraftFilters } from '@/types';

interface UseFilterOptionsProps {
  filters: WatercraftFilters;
}

export const useFilterOptions = ({ filters }: UseFilterOptionsProps) => {
  // Define filter options
  const watercraftTypes: WatercraftType[] = ['boat', 'kayak', 'PB', 'launch', 'surfski', 'coastal'];
  const boatTypes: BoatType[] = ['1x', '2x', '2-', '4x', '4+', '3x', '8+'];
  const statusOptions: WatercraftStatus[] = ['available', 'in-use', 'maintenance', 'regatta'];
  
  // Count active filters (excluding search)
  const activeFilterCount = 
    (filters.type.length > 0 ? 1 : 0) +
    (filters.status.length > 0 ? 1 : 0) +
    (filters.showFavoritesOnly ? 1 : 0);

  // Check if boat type filter should be shown
  const shouldShowBoatTypeFilter = filters.type.includes('boat');
  
  // Check if weight range filter should be shown
  const shouldShowWeightRangeFilter = filters.type.includes('boat');

  return {
    watercraftTypes,
    boatTypes,
    statusOptions,
    activeFilterCount,
    shouldShowBoatTypeFilter,
    shouldShowWeightRangeFilter,
  };
};
