import React from 'react';
import { WeatherStation } from '@/types/weather';
import { searchWeatherStations } from '@/services/weather.service';

export const useStationSearch = () => {
  const [query, setQuery] = React.useState('');
  const [searchResults, setSearchResults] = React.useState<WeatherStation[]>([]);
  const [loading, setLoading] = React.useState(false);

  // Handle station search with debouncing
  React.useEffect(() => {
    if (query.length > 1) {
      setLoading(true);

      const performSearch = async () => {
        try {
          const results = await searchWeatherStations(query);
          setSearchResults(results);
        } catch (error) {
          console.error('Error searching stations:', error);
          setSearchResults([]);
        } finally {
          setLoading(false);
        }
      };

      const timer = setTimeout(performSearch, 300);
      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
      setLoading(false);
    }
  }, [query]);

  return {
    query,
    setQuery,
    searchResults,
    loading,
  };
};
