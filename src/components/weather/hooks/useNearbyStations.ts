import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { WeatherStation } from '@/types/weather';
import { findNearbyStations } from '@/services/weather.service';
import { toast } from 'sonner';

export const useNearbyStations = () => {
  const { t } = useLanguage();
  const [nearbyStations, setNearbyStations] = React.useState<WeatherStation[]>([]);
  const [locationLoading, setLocationLoading] = React.useState(false);

  // Handle finding nearby stations using geolocation
  const handleFindNearby = async () => {
    if (!navigator.geolocation) {
      toast.error(t('weather_geolocation_not_supported'));
      return;
    }

    setLocationLoading(true);

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          const nearby = await findNearbyStations(latitude, longitude, 100); // 100km radius
          setNearbyStations(nearby);

          if (nearby.length === 0) {
            toast.info(t('weather_no_stations_found_nearby'));
          } else {
            toast.success(t('weather_found_nearby_stations', { count: nearby.length }));
          }
        } catch (error) {
          console.error('Error finding nearby stations:', error);
          toast.error(t('weather_failed_find_nearby'));
        } finally {
          setLocationLoading(false);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        toast.error(t('weather_location_permission_error'));
        setLocationLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  return {
    nearbyStations,
    locationLoading,
    handleFindNearby,
  };
};
