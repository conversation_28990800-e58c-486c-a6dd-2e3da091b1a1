import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Thermometer, Wind, Waves, Eye, Gauge } from 'lucide-react';
import { WeatherStation } from '@/types/weather';

interface StationCapabilityIconsProps {
  station: WeatherStation;
}

export const StationCapabilityIcons: React.FC<StationCapabilityIconsProps> = ({ station }) => {
  const { t } = useLanguage();

  if (!station.capabilities) return null;

  const icons = [];
  const { capabilities } = station;

  if (capabilities.airTemperature) {
    icons.push(
      <Thermometer 
        key="temp" 
        className="h-3 w-3 text-orange-500" 
        title={t('weather_capability_air_temperature')} 
      />
    );
  }
  
  if (capabilities.waterTemperature) {
    icons.push(
      <Thermometer 
        key="water-temp" 
        className="h-3 w-3 text-blue-500" 
        title={t('weather_capability_water_temperature')} 
      />
    );
  }
  
  if (capabilities.wind) {
    icons.push(
      <Wind 
        key="wind" 
        className="h-3 w-3 text-sky-500" 
        title={t('weather_capability_wind_data')} 
      />
    );
  }
  
  if (capabilities.waterLevel) {
    icons.push(
      <Waves 
        key="tide" 
        className="h-3 w-3 text-teal-500" 
        title={t('weather_capability_water_level')} 
      />
    );
  }
  
  if (capabilities.barometricPressure) {
    icons.push(
      <Gauge 
        key="pressure" 
        className="h-3 w-3 text-green-500" 
        title={t('weather_capability_barometric_pressure')} 
      />
    );
  }
  
  if (capabilities.visibility) {
    icons.push(
      <Eye 
        key="visibility" 
        className="h-3 w-3 text-purple-500" 
        title={t('weather_capability_visibility')} 
      />
    );
  }

  return icons.length > 0 ? (
    <div className="flex gap-1 mt-1">
      {icons}
    </div>
  ) : null;
};
