import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { Button } from '@/components/ui/button';
import { Navigation, Loader2, Search } from 'lucide-react';

interface StationSearchInputProps {
  query: string;
  onQueryChange: (query: string) => void;
  onFindNearby: () => void;
  locationLoading: boolean;
}

export const StationSearchInput: React.FC<StationSearchInputProps> = ({
  query,
  onQueryChange,
  onFindNearby,
  locationLoading,
}) => {
  const { t } = useLanguage();

  return (
    <div className="flex items-center border-b px-3">
      <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
      <input
        type="text"
        placeholder={t('weather_search_placeholder')}
        value={query}
        onChange={(e) => onQueryChange(e.target.value)}
        className="flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 border-0 focus:ring-0"
      />
      <Button
        variant="ghost"
        size="sm"
        onClick={onFindNearby}
        disabled={locationLoading}
        className="ml-2 h-8 w-8 p-0"
        title={t('weather_find_nearby_stations')}
      >
        {locationLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Navigation className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
};
