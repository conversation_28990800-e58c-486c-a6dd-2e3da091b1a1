
import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { useWeather } from '@/context/WeatherContext';
import { Button } from '@/components/ui/button';
import { MapPin } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandList } from '@/components/ui/command';
import { useIsMobile } from '@/hooks/use-mobile';
import { WeatherStation } from '@/types/weather';
import { detectStationCapabilities } from '@/services/weather.service';
import { StationSearchInput } from './StationSearchInput';
import { StationList } from './StationList';
import { useStationSearch } from '../hooks/useStationSearch';
import { useNearbyStations } from '../hooks/useNearbyStations';

interface StationSelectorProps {
  currentStation: WeatherStation | null;
  onSelectStation: (station: WeatherStation) => void;
}

export const StationSelector: React.FC<StationSelectorProps> = ({
  currentStation,
  onSelectStation,
}) => {
  const { t } = useLanguage();
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = React.useState(false);

  // Use custom hooks for search and nearby stations logic
  const { query, setQuery, searchResults, loading } = useStationSearch();
  const { nearbyStations, locationLoading, handleFindNearby } = useNearbyStations();

  // Handle station selection with capability detection
  const handleStationSelect = async (station: WeatherStation) => {
    try {
      // If station doesn't have capabilities, detect them
      if (!station.capabilities) {
        const capabilities = await detectStationCapabilities(station.id);
        station.capabilities = capabilities;
      }

      onSelectStation(station);
      setIsOpen(false);
    } catch (error) {
      console.error('Error detecting station capabilities:', error);
      // Still select the station even if capability detection fails
      onSelectStation(station);
      setIsOpen(false);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size={isMobile ? "default" : "sm"}
          className={`${isMobile ? 'h-10 w-[150px] text-sm' : 'h-7'} flex items-center`}
        >
          <MapPin className={`${isMobile ? 'h-4 w-4' : 'h-3 w-3'} mr-1 flex-shrink-0`} />
          <span className="truncate">
            {currentStation?.name || t('weather_station')}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[280px] lg:w-[350px]" align={isMobile ? "start" : "center"}>
        <Command shouldFilter={false}>
          <StationSearchInput
            query={query}
            onQueryChange={setQuery}
            onFindNearby={handleFindNearby}
            locationLoading={locationLoading}
          />
          <CommandEmpty>
            {loading ? t('weather_loading') : t('weather_no_results')}
          </CommandEmpty>
          <CommandList>
            <StationList
              nearbyStations={nearbyStations}
              searchResults={searchResults}
              query={query}
              onSelectStation={handleStationSelect}
            />
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
