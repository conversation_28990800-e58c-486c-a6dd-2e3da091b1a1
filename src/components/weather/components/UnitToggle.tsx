
import React from 'react';
import { WeatherUnit } from '@/types/weather';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useIsMobile } from '@/hooks/use-mobile';
import { useLanguage } from '@/context/LanguageContext';

interface UnitToggleProps {
  weatherUnit: WeatherUnit;
  setWeatherUnit: (unit: WeatherUnit) => void;
}

export const UnitToggle: React.FC<UnitToggleProps> = ({
  weatherUnit,
  setWeatherUnit
}) => {
  const isMobile = useIsMobile();
  const { t } = useLanguage();

  return (
    <ToggleGroup
      type="single"
      value={weatherUnit}
      onValueChange={(value: string) => {
        if (value) setWeatherUnit(value as WeatherUnit);
      }}
      className={`${isMobile ? 'h-8' : 'h-7'} border rounded-md overflow-hidden`}
    >
      <ToggleGroupItem
        value="metric"
        aria-label={t('weather_use_metric_units')}
        className={`px-2 ${isMobile ? 'text-sm h-8' : 'text-xs h-7'}`}
      >
        °C
      </ToggleGroupItem>
      <ToggleGroupItem
        value="imperial"
        aria-label={t('weather_use_imperial_units')}
        className={`px-2 ${isMobile ? 'text-sm h-8' : 'text-xs h-7'}`}
      >
        °F
      </ToggleGroupItem>
    </ToggleGroup>
  );
};
