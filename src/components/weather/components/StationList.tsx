import React from 'react';
import { useLanguage } from '@/context/LanguageContext';
import { CommandGroup, CommandSeparator } from '@/components/ui/command';
import { WeatherStation } from '@/types/weather';
import { StationItem } from './StationItem';

interface StationListProps {
  nearbyStations: WeatherStation[];
  searchResults: WeatherStation[];
  query: string;
  onSelectStation: (station: WeatherStation) => void;
}

export const StationList: React.FC<StationListProps> = ({
  nearbyStations,
  searchResults,
  query,
  onSelectStation,
}) => {
  const { t } = useLanguage();

  return (
    <>
      {nearbyStations.length > 0 && (
        <>
          <CommandGroup heading={t('weather_nearby_stations')}>
            {nearbyStations.slice(0, 5).map((station) => (
              <StationItem
                key={`nearby-${station.id}`}
                station={station}
                onSelect={onSelectStation}
                type="nearby"
              />
            ))}
          </CommandGroup>
          {searchResults.length > 0 && <CommandSeparator />}
        </>
      )}
      
      {searchResults.length > 0 && (
        <CommandGroup heading={query ? t('weather_search_results') : t('weather_popular_stations')}>
          {searchResults.map((station) => (
            <StationItem
              key={`search-${station.id}`}
              station={station}
              onSelect={onSelectStation}
              type="search"
            />
          ))}
        </CommandGroup>
      )}
    </>
  );
};
