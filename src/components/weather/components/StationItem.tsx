import React from 'react';
import { useWeather } from '@/context/WeatherContext';
import { CommandItem } from '@/components/ui/command';
import { MapPin, Navigation } from 'lucide-react';
import { WeatherStation } from '@/types/weather';
import { StationCapabilityIcons } from './StationCapabilityIcons';

interface StationItemProps {
  station: WeatherStation;
  onSelect: (station: WeatherStation) => void;
  type: 'nearby' | 'search';
}

export const StationItem: React.FC<StationItemProps> = ({ station, onSelect, type }) => {
  const { weatherUnit } = useWeather();

  // Format distance according to user's unit preference
  const formatDistance = (distanceKm: number): string => {
    if (weatherUnit === 'imperial') {
      const miles = distanceKm * 0.621371;
      return `${miles.toFixed(1)} mi`;
    }
    return `${distanceKm.toFixed(1)} km`;
  };

  const IconComponent = type === 'nearby' ? Navigation : MapPin;
  const iconColor = type === 'nearby' ? 'text-blue-600' : 'text-green-600';

  return (
    <CommandItem
      key={`${type}-${station.id}`}
      value={`${type}-${station.id}-${station.name}`}
      onSelect={() => onSelect(station)}
    >
      <IconComponent className={`h-4 w-4 mr-2 ${iconColor}`} />
      <div className="flex-1">
        <div>
          <span>{station.name}</span>
          {station.state && (
            <span className="ml-1 text-muted-foreground">({station.state})</span>
          )}
        </div>
        <StationCapabilityIcons station={station} />
      </div>
      {station.distance && (
        <span className="text-xs text-muted-foreground ml-2">
          {formatDistance(station.distance)}
        </span>
      )}
    </CommandItem>
  );
};
