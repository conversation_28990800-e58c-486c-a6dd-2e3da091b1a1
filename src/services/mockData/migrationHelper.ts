/**
 * Migration Helper for Centralized Mock Data
 * 
 * This file helps transition from the old scattered mock data files
 * to the new centralized mock data system. It provides backward compatibility
 * while allowing gradual migration.
 */

import {
  getCentralizedUsers,
  getCentralizedWatercrafts,
  getCentralizedMaintenanceRequests,
  getCentralizedJaunts,
  validateDataIntegrity
} from './centralizedMockData';

// Re-export centralized data with old naming for backward compatibility
export const mockUsers = getCentralizedUsers();
export const mockWatercrafts = getCentralizedWatercrafts();
export const mockBoats = getCentralizedWatercrafts().filter(craft => craft.type === 'boat');
export const mockMaintenanceRequests = getCentralizedMaintenanceRequests();
export const mockJaunts = getCentralizedJaunts();

// Helper function to get maintenance count for a watercraft (used in existing code)
export const getMaintenanceCountForWatercraft = (watercraftId: string): number => {
  return mockMaintenanceRequests.filter(req => req.watercraftId === watercraftId).length;
};

// Validation function to ensure data integrity
export const validateMockDataIntegrity = () => {
  const validation = validateDataIntegrity();
  
  if (!validation.isValid) {
    console.warn('Mock data integrity issues found:', validation.errors);
  } else {
    console.log('Mock data integrity validation passed');
  }
  
  return validation;
};

// Initialize validation on import (only in development)
if (process.env.NODE_ENV === 'development') {
  validateMockDataIntegrity();
}

// Export centralized functions for new code
export {
  getCentralizedUsers,
  getCentralizedWatercrafts,
  getCentralizedMaintenanceRequests,
  getCentralizedJaunts,
  getUserById,
  getWatercraftById,
  getMaintenanceRequestsByWatercraft,
  getJauntsByUser,
  getJauntsByWatercraft,
  MOCK_IDS
} from './centralizedMockData';

// Export ID generators for backward compatibility
export {
  generateId,
  generateWatercraftId,
  generateMaintenanceId,
  generateJauntId,
  generateUserId,
  generateTeamId
} from '@/utils/idGenerator';
