/**
 * Centralized Mock Data Management
 * 
 * This file provides a single source of truth for all mock data in the application.
 * It consolidates data from multiple scattered files and provides consistent IDs and relationships.
 */

import { Watercraft, Boat, User, MaintenanceRequest, Jaunt } from '@/types';

// Centralized ID management - using consistent IDs across all mock data
export const MOCK_IDS = {
  // Users
  ADMIN_USER: 'user-admin-001',
  COACH_USER_1: 'user-coach-001',
  COACH_USER_2: 'user-coach-002',
  MEMBER_USER_1: 'user-member-001',
  MEMBER_USER_2: 'user-member-002',
  MEMBER_USER_3: 'user-member-003',
  
  // Watercrafts
  BOAT_SWIFT_ARROW: 'craft-boat-001',
  BOAT_BLUE_DOLPHIN: 'craft-boat-002',
  BOAT_EAGLE_EYE: 'craft-boat-003',
  BOAT_COMPETITION_8: 'craft-boat-004',
  BOAT_TRAINER_DOUBLE: 'craft-boat-005',
  K<PERSON>Y<PERSON><PERSON>_WATER_STRIDER: 'craft-kayak-001',
  KA<PERSON>A<PERSON>_SILVER_STREAK: 'craft-kayak-002',
  SURFSKI_WIND_CHASER: 'craft-surfski-001',
  SURFSKI_PRO: 'craft-surfski-002',
  PB_SEA_RUNNER: 'craft-pb-001',
  LAUNCH_WAVE_RIDER: 'craft-launch-001',
  LAUNCH_SAFETY: 'craft-launch-002',
  COASTAL_CRUISER: 'craft-coastal-001',
  COASTAL_RACER: 'craft-coastal-002',
  
  // Maintenance Requests
  MAINTENANCE_001: 'maint-001',
  MAINTENANCE_002: 'maint-002',
  MAINTENANCE_003: 'maint-003',
  MAINTENANCE_004: 'maint-004',
  MAINTENANCE_005: 'maint-005',
  
  // Jaunts
  JAUNT_001: 'jaunt-001',
  JAUNT_002: 'jaunt-002',
  JAUNT_003: 'jaunt-003',
  JAUNT_004: 'jaunt-004',
  JAUNT_005: 'jaunt-005',
} as const;

// Base timestamps for consistent data
const baseDate = new Date('2024-01-01');
const now = new Date();

// Centralized Users Data
export const centralizedUsers: User[] = [
  {
    id: MOCK_IDS.ADMIN_USER,
    name: 'Alex Johnson',
    email: '<EMAIL>',
    phone: '************',
    role: 'admin',
    skills: ['Rowing', 'Coaching', 'Maintenance'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 3 },
      { watercraftType: 'kayak', skillLevel: 3 },
      { watercraftType: 'PB', skillLevel: 3 },
      { watercraftType: 'launch', skillLevel: 3 },
      { watercraftType: 'surfski', skillLevel: 3 },
      { watercraftType: 'coastal', skillLevel: 3 },
    ],
    language: 'en',
    favorites: [MOCK_IDS.BOAT_SWIFT_ARROW, MOCK_IDS.KAYAK_WATER_STRIDER],
    createdAt: baseDate,
    updatedAt: now,
  },
  {
    id: MOCK_IDS.COACH_USER_1,
    name: 'Samantha Carter',
    email: '<EMAIL>',
    phone: '************',
    role: 'coach',
    skills: ['Rowing', 'Coaching'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 2 },
      { watercraftType: 'kayak', skillLevel: 1 },
      { watercraftType: 'PB', skillLevel: 2 },
      { watercraftType: 'launch', skillLevel: 2 },
    ],
    language: 'en',
    favorites: [MOCK_IDS.BOAT_TRAINER_DOUBLE],
    createdAt: baseDate,
    updatedAt: now,
  },
  {
    id: MOCK_IDS.COACH_USER_2,
    name: 'Sarah Rodriguez',
    email: '<EMAIL>',
    phone: '************',
    role: 'coach',
    skills: ['Rowing', 'Coaching', 'Coxswain'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 3 },
      { watercraftType: 'kayak', skillLevel: 2 },
      { watercraftType: 'launch', skillLevel: 3 },
    ],
    language: 'es',
    favorites: [],
    createdAt: baseDate,
    updatedAt: now,
  },
  {
    id: MOCK_IDS.MEMBER_USER_1,
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 1 },
      { watercraftType: 'kayak', skillLevel: 0 },
    ],
    language: 'en',
    favorites: [],
    createdAt: baseDate,
    updatedAt: now,
  },
  {
    id: MOCK_IDS.MEMBER_USER_2,
    name: 'Emma Wilson',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 1 },
    ],
    language: 'en',
    favorites: [],
    createdAt: baseDate,
    updatedAt: now,
  },
  {
    id: MOCK_IDS.MEMBER_USER_3,
    name: 'David Kim',
    email: '<EMAIL>',
    phone: '************',
    role: 'member',
    skills: ['Rowing', 'Maintenance'],
    permissions: [
      { watercraftType: 'boat', skillLevel: 2 },
      { watercraftType: 'surfski', skillLevel: 1 },
    ],
    language: 'ko',
    favorites: [],
    createdAt: baseDate,
    updatedAt: now,
  },
];

// Centralized Watercrafts Data
export const centralizedWatercrafts: Watercraft[] = [
  // Boats
  {
    id: MOCK_IDS.BOAT_SWIFT_ARROW,
    name: "Swift Arrow",
    type: "boat",
    ownershipType: "club",
    location: "Bay A, Rack 3",
    imageUrl: "https://images.unsplash.com/photo-1535535112387-56ffe8db21ff?q=80&w=600&auto=format&fit=crop",
    skillLevel: 1,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  } as Boat,
  {
    id: MOCK_IDS.BOAT_BLUE_DOLPHIN,
    name: "Blue Dolphin",
    type: "boat",
    ownershipType: "club",
    location: "Bay A, Rack 2",
    imageUrl: "https://images.unsplash.com/photo-1564842223950-a6ea8052e79e?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "maintenance",
    createdAt: baseDate,
    updatedAt: now
  } as Boat,
  {
    id: MOCK_IDS.BOAT_EAGLE_EYE,
    name: "Eagle Eye",
    type: "boat",
    ownershipType: "club",
    location: "Bay A, Rack 1",
    imageUrl: "https://images.unsplash.com/photo-1595836956462-1de23a3d80e6?q=80&w=600&auto=format&fit=crop",
    skillLevel: 0,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  } as Boat,
  {
    id: MOCK_IDS.BOAT_COMPETITION_8,
    name: "Competition 8+",
    type: "boat",
    ownershipType: "club",
    location: "Race Preparation Area",
    imageUrl: "https://images.unsplash.com/photo-1541443458929-9d6cbd3d2513?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "regatta",
    createdAt: baseDate,
    updatedAt: now
  } as Boat,
  {
    id: MOCK_IDS.BOAT_TRAINER_DOUBLE,
    name: "Trainer Double",
    type: "boat",
    ownershipType: "club",
    location: "Training Area B",
    imageUrl: "https://images.unsplash.com/photo-1532643076501-5ad2201be430?q=80&w=600&auto=format&fit=crop",
    skillLevel: 1,
    status: "in-use",
    createdAt: baseDate,
    updatedAt: now
  } as Boat,

  // Kayaks
  {
    id: MOCK_IDS.KAYAK_WATER_STRIDER,
    name: "Water Strider",
    type: "kayak",
    ownershipType: "club",
    location: "Bay B, Rack 1",
    imageUrl: "https://images.unsplash.com/photo-1520454083703-778d6da1af02?q=80&w=600&auto=format&fit=crop",
    skillLevel: 0,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  },
  {
    id: MOCK_IDS.KAYAK_SILVER_STREAK,
    name: "Silver Streak",
    type: "kayak",
    ownershipType: "club",
    location: "Bay B, Rack 2",
    imageUrl: "https://images.unsplash.com/photo-1562610744-7c427b7e7d2f?q=80&w=600&auto=format&fit=crop",
    skillLevel: 0,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  },

  // Surfskis
  {
    id: MOCK_IDS.SURFSKI_WIND_CHASER,
    name: "Wind Chaser",
    type: "surfski",
    ownershipType: "member",
    memberId: MOCK_IDS.MEMBER_USER_3,
    location: "Bay C, Rack 1",
    imageUrl: "https://images.unsplash.com/photo-1580332449238-a72634d3876b?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  },
  {
    id: MOCK_IDS.SURFSKI_PRO,
    name: "Surfski Pro",
    type: "surfski",
    ownershipType: "club",
    location: "Competition Storage",
    imageUrl: "https://images.unsplash.com/photo-1566008033935-e9f529c17b77?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "maintenance",
    createdAt: baseDate,
    updatedAt: now
  },

  // Paddle Boards
  {
    id: MOCK_IDS.PB_SEA_RUNNER,
    name: "Sea Runner",
    type: "PB",
    ownershipType: "club",
    location: "Bay B, Rack 4",
    imageUrl: "https://images.unsplash.com/photo-1602568584853-71ce86840276?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "in-use",
    createdAt: baseDate,
    updatedAt: now
  },

  // Launches
  {
    id: MOCK_IDS.LAUNCH_WAVE_RIDER,
    name: "Wave Rider",
    type: "launch",
    ownershipType: "club",
    location: "Marina Dock B",
    imageUrl: "https://images.unsplash.com/photo-1618640399586-4ea62f9688e8?q=80&w=600&auto=format&fit=crop",
    skillLevel: 3,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  },
  {
    id: MOCK_IDS.LAUNCH_SAFETY,
    name: "Safety Launch",
    type: "launch",
    ownershipType: "club",
    location: "Safety Dock",
    imageUrl: "https://images.unsplash.com/photo-1554253449-cfcc0c4b6b43?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  },

  // Coastal
  {
    id: MOCK_IDS.COASTAL_CRUISER,
    name: "Coastal Cruiser",
    type: "coastal",
    ownershipType: "club",
    location: "Bay D, Rack 3",
    imageUrl: "https://images.unsplash.com/photo-1589307197668-6c28382b5b68?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  },
  {
    id: MOCK_IDS.COASTAL_RACER,
    name: "Coastal Racer",
    type: "coastal",
    ownershipType: "member",
    memberId: MOCK_IDS.MEMBER_USER_2,
    location: "Member Storage A2",
    imageUrl: "https://images.unsplash.com/photo-1590179406383-a8e58ddb7652?q=80&w=600&auto=format&fit=crop",
    skillLevel: 2,
    status: "available",
    createdAt: baseDate,
    updatedAt: now
  },
];

// Centralized Maintenance Requests Data
export const centralizedMaintenanceRequests: MaintenanceRequest[] = [
  {
    id: MOCK_IDS.MAINTENANCE_001,
    watercraftId: MOCK_IDS.BOAT_BLUE_DOLPHIN,
    requestDate: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    watercraftState: "unuseable",
    issueType: "broken",
    note: "Rudder mechanism is stuck in one position.",
    status: "in-progress",
    createdAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000)
  },
  {
    id: MOCK_IDS.MAINTENANCE_002,
    watercraftId: MOCK_IDS.SURFSKI_PRO,
    requestDate: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    watercraftState: "unuseable",
    issueType: "damaged",
    note: "Severe impact damage to the bow section.",
    status: "in-progress",
    createdAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
  },
  {
    id: MOCK_IDS.MAINTENANCE_003,
    watercraftId: MOCK_IDS.BOAT_EAGLE_EYE,
    requestDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
    watercraftState: "useable",
    issueType: "missing part",
    note: "Stretcher adjustment mechanism is loose.",
    status: "closed",
    resolution: "Replaced with new parts and secured properly.",
    resolutionDate: new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000), // 8 days ago
    createdAt: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000)
  },
  {
    id: MOCK_IDS.MAINTENANCE_004,
    watercraftId: MOCK_IDS.BOAT_TRAINER_DOUBLE,
    requestDate: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    watercraftState: "useable",
    issueType: "broken",
    note: "Oar holder clamps need adjustment, not closing properly.",
    status: "open",
    createdAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
  },
  {
    id: MOCK_IDS.MAINTENANCE_005,
    watercraftId: MOCK_IDS.LAUNCH_SAFETY,
    requestDate: new Date(now.getTime() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
    watercraftState: "unuseable",
    issueType: "broken",
    note: "Engine not starting, possible fuel system issue.",
    status: "closed",
    resolution: "Fuel system cleaned and carburetor rebuilt. Working properly now.",
    resolutionDate: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
    createdAt: new Date(now.getTime() - 20 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000)
  },
];

// Centralized Jaunts Data
export const centralizedJaunts: Jaunt[] = [
  {
    id: MOCK_IDS.JAUNT_001,
    userId: MOCK_IDS.MEMBER_USER_1,
    watercraftId: MOCK_IDS.BOAT_SWIFT_ARROW,
    startTime: new Date('2024-06-10T07:30:00'),
    plannedEndTime: new Date('2024-06-10T09:30:00'),
    actualEndTime: new Date('2024-06-10T09:15:00'),
    comments: 'Early morning training session, excellent conditions',
    createdAt: new Date('2024-06-09T18:00:00'),
    updatedAt: new Date('2024-06-10T09:15:00'),
  },
  {
    id: MOCK_IDS.JAUNT_002,
    userId: MOCK_IDS.COACH_USER_1,
    watercraftId: MOCK_IDS.BOAT_TRAINER_DOUBLE,
    startTime: new Date('2024-06-12T15:00:00'),
    plannedEndTime: new Date('2024-06-12T17:00:00'),
    comments: 'Team practice for upcoming regatta',
    createdAt: new Date('2024-06-11T10:00:00'),
    updatedAt: new Date('2024-06-11T10:00:00'),
  },
  {
    id: MOCK_IDS.JAUNT_003,
    userId: MOCK_IDS.MEMBER_USER_2,
    watercraftId: MOCK_IDS.KAYAK_WATER_STRIDER,
    startTime: new Date('2024-06-15T12:00:00'),
    plannedEndTime: new Date('2024-06-15T13:30:00'),
    actualEndTime: new Date('2024-06-15T14:00:00'),
    comments: 'Lunchtime paddle, stayed out longer due to perfect weather',
    createdAt: new Date('2024-06-14T19:00:00'),
    updatedAt: new Date('2024-06-15T14:00:00'),
  },
  {
    id: MOCK_IDS.JAUNT_004,
    userId: MOCK_IDS.ADMIN_USER,
    watercraftId: MOCK_IDS.LAUNCH_SAFETY,
    startTime: new Date('2024-06-18T08:00:00'),
    plannedEndTime: new Date('2024-06-18T12:00:00'),
    comments: 'Safety boat duty for junior training session',
    createdAt: new Date('2024-06-17T15:00:00'),
    updatedAt: new Date('2024-06-17T15:00:00'),
  },
  {
    id: MOCK_IDS.JAUNT_005,
    userId: MOCK_IDS.COACH_USER_2,
    watercraftId: MOCK_IDS.PB_SEA_RUNNER,
    startTime: new Date(now.getTime() - 60 * 60 * 1000), // 1 hour ago
    plannedEndTime: new Date(now.getTime() + 60 * 60 * 1000), // 1 hour from now
    comments: 'Current training session with team',
    createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000),
    updatedAt: new Date(now.getTime() - 60 * 60 * 1000),
  },
];

// Export functions for accessing centralized data
export const getCentralizedUsers = (): User[] => JSON.parse(JSON.stringify(centralizedUsers));
export const getCentralizedWatercrafts = (): Watercraft[] => JSON.parse(JSON.stringify(centralizedWatercrafts));
export const getCentralizedMaintenanceRequests = (): MaintenanceRequest[] => JSON.parse(JSON.stringify(centralizedMaintenanceRequests));
export const getCentralizedJaunts = (): Jaunt[] => JSON.parse(JSON.stringify(centralizedJaunts));

// Helper functions for data relationships
export const getUserById = (id: string): User | undefined => centralizedUsers.find(user => user.id === id);
export const getWatercraftById = (id: string): Watercraft | undefined => centralizedWatercrafts.find(craft => craft.id === id);
export const getMaintenanceRequestsByWatercraft = (watercraftId: string): MaintenanceRequest[] =>
  centralizedMaintenanceRequests.filter(req => req.watercraftId === watercraftId);
export const getJauntsByUser = (userId: string): Jaunt[] =>
  centralizedJaunts.filter(jaunt => jaunt.userId === userId);
export const getJauntsByWatercraft = (watercraftId: string): Jaunt[] =>
  centralizedJaunts.filter(jaunt => jaunt.watercraftId === watercraftId);

// Data validation helpers
export const validateDataIntegrity = () => {
  const errors: string[] = [];

  // Check that all referenced user IDs exist
  centralizedJaunts.forEach(jaunt => {
    if (!getUserById(jaunt.userId)) {
      errors.push(`Jaunt ${jaunt.id} references non-existent user ${jaunt.userId}`);
    }
  });

  // Check that all referenced watercraft IDs exist
  centralizedJaunts.forEach(jaunt => {
    if (!getWatercraftById(jaunt.watercraftId)) {
      errors.push(`Jaunt ${jaunt.id} references non-existent watercraft ${jaunt.watercraftId}`);
    }
  });

  centralizedMaintenanceRequests.forEach(req => {
    if (!getWatercraftById(req.watercraftId)) {
      errors.push(`Maintenance request ${req.id} references non-existent watercraft ${req.watercraftId}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};
