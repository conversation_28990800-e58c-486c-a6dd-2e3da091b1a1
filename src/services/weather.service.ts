
import { WeatherStation, WeatherData, HourlyForecast, WeatherAlerts, WeatherAlert, WeatherStationCapabilities } from '@/types/weather';

// Default station for San Francisco Bay
export const DEFAULT_STATION: WeatherStation = {
  id: '9414290',
  name: 'San Francisco, CA',
  lat: 37.8063,
  lon: -122.4659,
  state: 'CA'
};

// List of popular NOAA stations for quick access
export const POPULAR_STATIONS: WeatherStation[] = [
  { id: '9414290', name: 'San Francisco, CA', lat: 37.8063, lon: -122.4659, state: 'CA' },
  { id: '9414523', name: 'Redwood City, CA', lat: 37.5067, lon: -122.2100, state: 'CA' },
  { id: '9414750', name: 'Alameda, CA', lat: 37.7717, lon: -122.3000, state: 'CA' },
  { id: '9414863', name: 'Richmond, CA', lat: 37.9300, lon: -122.4100, state: 'CA' },
  { id: '9415020', name: 'Point Reyes, CA', lat: 38.0050, lon: -122.9767, state: 'CA' },
  { id: '9413450', name: 'Monterey, CA', lat: 36.6050, lon: -121.8883, state: 'CA' },
  { id: '9410230', name: 'San Diego, CA', lat: 32.7142, lon: -117.1736, state: 'CA' },
  { id: '8418150', name: 'Boston, MA', lat: 42.3584, lon: -71.0498, state: 'MA' },
  { id: '8518750', name: 'The Battery, NY', lat: 40.7000, lon: -74.0150, state: 'NY' },
  { id: '8594900', name: 'Washington, DC', lat: 38.8744, lon: -77.0213, state: 'DC' },
  { id: '8638863', name: 'Sewells Point, VA', lat: 36.9467, lon: -76.3300, state: 'VA' },
  { id: '8665530', name: 'Charleston, SC', lat: 32.7806, lon: -79.9256, state: 'SC' },
  { id: '8723970', name: 'Miami Beach, FL', lat: 25.7686, lon: -80.1300, state: 'FL' },
  { id: '8771450', name: 'Galveston, TX', lat: 29.3100, lon: -94.7900, state: 'TX' },
  { id: '9431647', name: 'Neah Bay, WA', lat: 48.3683, lon: -124.6017, state: 'WA' },
  { id: '9447130', name: 'Seattle, WA', lat: 47.6026, lon: -122.3393, state: 'WA' },
];

/**
 * Search weather stations by name using NOAA station metadata API
 */
export const searchWeatherStations = async (query: string): Promise<WeatherStation[]> => {
  try {
    console.log(`Searching for weather stations with query: "${query}"`);

    if (query.length < 2) {
      return [];
    }

    // Clean and normalize the query
    const cleanQuery = query.trim();

    // First, search popular stations for quick results
    const popularResults = POPULAR_STATIONS.filter(station => {
      const stationName = station.name.toLowerCase();
      const stationState = (station.state || '').toLowerCase();
      const queryLower = cleanQuery.toLowerCase();

      // Support various search patterns:
      // - "redwood city" matches "Redwood City, CA"
      // - "redwood city, ca" matches "Redwood City, CA"
      // - "ca" matches stations in CA
      // - "9414523" matches station ID
      return stationName.includes(queryLower) ||
             stationState.includes(queryLower) ||
             station.id.includes(cleanQuery) ||
             // Support city name without state
             stationName.split(',')[0].toLowerCase().includes(queryLower);
    });

    console.log(`Found ${popularResults.length} popular stations matching "${cleanQuery}"`);

    // Make API call for comprehensive search
    const data = await fetchFromAPI(`/weather/stations/search?q=${encodeURIComponent(cleanQuery)}`);
    console.log(`API returned ${data?.length || 0} stations for "${cleanQuery}"`);

    return data || [];
  } catch (error) {
    console.error('Error searching stations:', error);

    // Enhanced fallback to popular stations with better matching
    const fallbackResults = POPULAR_STATIONS.filter(station => {
      const stationName = station.name.toLowerCase();
      const stationState = (station.state || '').toLowerCase();
      const queryLower = query.toLowerCase().trim();

      return stationName.includes(queryLower) ||
             stationState.includes(queryLower) ||
             station.id.includes(query.trim()) ||
             stationName.split(',')[0].toLowerCase().includes(queryLower);
    });

    console.log(`Fallback: Found ${fallbackResults.length} popular stations matching "${query}"`);
    return fallbackResults;
  }
};

/**
 * Find nearby weather stations based on coordinates
 */
export const findNearbyStations = async (lat: number, lon: number, radiusKm: number = 50): Promise<WeatherStation[]> => {
  try {
    console.log(`Finding stations near ${lat}, ${lon} within ${radiusKm}km`);

    const data = await fetchFromAPI(`/weather/stations/nearby?lat=${lat}&lon=${lon}&radius=${radiusKm}`);
    return data;
  } catch (error) {
    console.error('Error finding nearby stations:', error);

    // Fallback to popular stations with distance calculation
    return calculateDistanceToPopularStations(lat, lon).slice(0, 10);
  }
};

/**
 * Calculate distance between two coordinates using Haversine formula
 */
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

/**
 * Calculate distances to popular stations and sort by proximity
 */
const calculateDistanceToPopularStations = (lat: number, lon: number): WeatherStation[] => {
  return POPULAR_STATIONS.map(station => ({
    ...station,
    distance: Math.round(calculateDistance(lat, lon, station.lat, station.lon) * 10) / 10
  })).sort((a, b) => (a.distance || 0) - (b.distance || 0));
};

/**
 * Validate and parse NOAA API response data
 */
const validateAndParseValue = (value: any, fieldName: string): number | null => {
  if (value === null || value === undefined || value === '') {
    return null;
  }

  const parsed = parseFloat(value);
  if (isNaN(parsed)) {
    console.warn(`Invalid ${fieldName} value: ${value}`);
    return null;
  }

  return parsed;
};

// API base URL - use environment variable or default to localhost
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

/**
 * Fetch data from our server API with error handling and retries
 */
const fetchFromAPI = async (endpoint: string, retries: number = 2): Promise<any> => {
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Check for API error responses
      if (data.error) {
        throw new Error(`API Error: ${data.error}`);
      }

      return data;
    } catch (error) {
      console.warn(`API attempt ${attempt + 1} failed:`, error);

      if (attempt === retries) {
        throw error;
      }

      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};



/**
 * Get current weather data for a station using NOAA APIs
 */
export const getWeatherData = async (stationId: string): Promise<WeatherData> => {
  console.log(`Fetching weather data for station: ${stationId}`);

  try {
    const data = await fetchFromAPI(`/weather/data/${stationId}`);
    return data;
  } catch (error) {
    console.error(`Error fetching weather data for station ${stationId}:`, error);
    throw error;
  }
};

/**
 * Get station information including coordinates
 */
const getStationInfo = async (stationId: string): Promise<WeatherStation | null> => {
  try {
    // First check if it's in our popular stations list
    const popularStation = POPULAR_STATIONS.find(s => s.id === stationId);
    if (popularStation) {
      return popularStation;
    }

    // Otherwise, fetch from NOAA API
    const response = await fetchNOAAData(
      `https://api.tidesandcurrents.noaa.gov/mdapi/prod/webapi/stations/${stationId}.json`
    );

    if (response?.stations?.[0]) {
      const station = response.stations[0];
      return {
        id: station.id,
        name: station.name,
        lat: parseFloat(station.lat),
        lon: parseFloat(station.lng),
        state: station.state
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching station info:', error);
    return null;
  }
};

/**
 * Get hourly forecast for a station using our server API
 */
export const getHourlyForecast = async (stationId: string): Promise<HourlyForecast> => {
  console.log(`Fetching hourly forecast for station: ${stationId}`);

  try {
    const data = await fetchFromAPI(`/weather/forecast/${stationId}`);
    return data;
  } catch (error) {
    console.error('Error fetching hourly forecast:', error);
    throw error; // Re-throw error since we now expect real NOAA data from server
  }
};



/**
 * Parse wind speed from NWS forecast text (e.g., "10 mph" -> 16.09 km/h)
 */
const parseWindSpeed = (windSpeedText: string): number | null => {
  if (!windSpeedText) return null;

  const match = windSpeedText.match(/(\d+)\s*mph/i);
  if (match) {
    const mph = parseInt(match[1]);
    return mph * 1.60934; // Convert mph to km/h
  }

  return null;
};

/**
 * Parse wind direction from NWS forecast text (e.g., "NW" -> 315 degrees)
 */
const parseWindDirection = (windDirectionText: string): number | null => {
  if (!windDirectionText) return null;

  const directions: { [key: string]: number } = {
    'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
    'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
    'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
    'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
  };

  const direction = windDirectionText.trim().toUpperCase();
  return directions[direction] || null;
};

/**
 * Detect what data types a station provides by testing API endpoints
 */
export const detectStationCapabilities = async (stationId: string): Promise<WeatherStationCapabilities> => {
  console.log(`Detecting capabilities for station: ${stationId}`);

  const capabilities: WeatherStationCapabilities = {
    airTemperature: false,
    waterTemperature: false,
    wind: false,
    waterLevel: false,
    predictions: false,
    barometricPressure: false,
    visibility: false
  };

  // Test each data type by making a request for recent data
  const testEndpoints = [
    {
      capability: 'airTemperature' as keyof WeatherStationCapabilities,
      product: 'air_temperature'
    },
    {
      capability: 'waterTemperature' as keyof WeatherStationCapabilities,
      product: 'water_temperature'
    },
    {
      capability: 'wind' as keyof WeatherStationCapabilities,
      product: 'wind'
    },
    {
      capability: 'waterLevel' as keyof WeatherStationCapabilities,
      product: 'water_level'
    },
    {
      capability: 'predictions' as keyof WeatherStationCapabilities,
      product: 'predictions'
    },
    {
      capability: 'barometricPressure' as keyof WeatherStationCapabilities,
      product: 'air_pressure'
    },
    {
      capability: 'visibility' as keyof WeatherStationCapabilities,
      product: 'visibility'
    }
  ];

  // Test each endpoint concurrently
  const tests = testEndpoints.map(async ({ capability, product }) => {
    try {
      let url;
      if (product === 'predictions') {
        // Predictions use a different endpoint format
        const today = new Date().toISOString().split('T')[0];
        url = `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&begin_date=${today}&end_date=${today}&time_zone=lst_ldt&datum=MLLW&units=metric&format=json&product=${product}`;
      } else {
        url = `https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?station=${stationId}&date=latest&time_zone=lst_ldt&units=metric&format=json&product=${product}`;
      }

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'BoatBook/1.0 (Weather Station App)'
        }
      });

      if (response.ok) {
        const data = await response.json();

        // Check if we got valid data (not an error response)
        if (product === 'predictions') {
          capabilities[capability] = !!(data?.predictions && data.predictions.length > 0);
        } else {
          capabilities[capability] = !!(data?.data && data.data.length > 0 && !data.error);
        }
      }
    } catch (error) {
      // Capability not available
      console.debug(`Station ${stationId} does not support ${capability}:`, error);
    }
  });

  // Wait for all tests to complete
  await Promise.allSettled(tests);

  console.log(`Station ${stationId} capabilities:`, capabilities);
  return capabilities;
};
