
/**
 * Watercraft Mock Data - Centralized
 *
 * This file now uses the centralized mock data system for consistency
 * and maintainability. The old scattered files are deprecated.
 */

import { Watercraft, MaintenanceRequest } from "@/types";
import {
  getCentralizedWatercrafts,
  getCentralizedMaintenanceRequests
} from "@/services/mockData/centralizedMockData";

// Export centralized data
export const mockWatercrafts: Watercraft[] = getCentralizedWatercrafts();
export const mockMaintenanceRequests: MaintenanceRequest[] = getCentralizedMaintenanceRequests();

// Helper to create a clean copy of the mock data to prevent reference issues
export const getCleanMockWatercrafts = (): Watercraft[] => {
  return JSON.parse(JSON.stringify(mockWatercrafts));
};
