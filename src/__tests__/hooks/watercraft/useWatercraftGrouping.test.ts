import { describe, it, expect } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useWatercraftGrouping } from '@/components/watercraft/hooks/useWatercraftGrouping';
import { Watercraft } from '@/types';

describe('useWatercraftGrouping', () => {
  const mockWatercrafts: Watercraft[] = [
    {
      id: 'boat-1',
      name: 'Test Boat 1',
      type: 'boat',
      ownershipType: 'club',
      location: 'Bay A',
      skillLevel: 1,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'boat-2',
      name: 'Test Boat 2',
      type: 'boat',
      ownershipType: 'club',
      location: 'Bay B',
      skillLevel: 2,
      status: 'in-use',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'kayak-1',
      name: 'Test Kayak 1',
      type: 'kayak',
      ownershipType: 'club',
      location: 'Bay C',
      skillLevel: 0,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'surfski-1',
      name: 'Test Surfski 1',
      type: 'surfski',
      ownershipType: 'member',
      memberId: 'member-1',
      location: 'Bay D',
      skillLevel: 3,
      status: 'maintenance',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'pb-1',
      name: 'Test PB 1',
      type: 'PB',
      ownershipType: 'club',
      location: 'Bay E',
      skillLevel: 1,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  it('groups watercrafts by type correctly', () => {
    const { result } = renderHook(() => useWatercraftGrouping(mockWatercrafts));

    const { groupedWatercrafts } = result.current;

    expect(groupedWatercrafts).toHaveProperty('boat');
    expect(groupedWatercrafts).toHaveProperty('kayak');
    expect(groupedWatercrafts).toHaveProperty('surfski');
    expect(groupedWatercrafts).toHaveProperty('PB');

    expect(groupedWatercrafts.boat).toHaveLength(2);
    expect(groupedWatercrafts.kayak).toHaveLength(1);
    expect(groupedWatercrafts.surfski).toHaveLength(1);
    expect(groupedWatercrafts.PB).toHaveLength(1);
  });

  it('returns correct watercrafts in each group', () => {
    const { result } = renderHook(() => useWatercraftGrouping(mockWatercrafts));

    const { groupedWatercrafts } = result.current;

    expect(groupedWatercrafts.boat[0].name).toBe('Test Boat 1');
    expect(groupedWatercrafts.boat[1].name).toBe('Test Boat 2');
    expect(groupedWatercrafts.kayak[0].name).toBe('Test Kayak 1');
    expect(groupedWatercrafts.surfski[0].name).toBe('Test Surfski 1');
    expect(groupedWatercrafts.PB[0].name).toBe('Test PB 1');
  });

  it('handles empty watercraft array', () => {
    const { result } = renderHook(() => useWatercraftGrouping([]));

    const { groupedWatercrafts } = result.current;

    expect(groupedWatercrafts).toEqual({});
  });

  it('handles single watercraft type', () => {
    const singleTypeWatercrafts = mockWatercrafts.filter(w => w.type === 'boat');
    const { result } = renderHook(() => useWatercraftGrouping(singleTypeWatercrafts));

    const { groupedWatercrafts } = result.current;

    expect(Object.keys(groupedWatercrafts)).toHaveLength(1);
    expect(groupedWatercrafts).toHaveProperty('boat');
    expect(groupedWatercrafts.boat).toHaveLength(2);
  });

  it('preserves watercraft order within groups', () => {
    const { result } = renderHook(() => useWatercraftGrouping(mockWatercrafts));

    const { groupedWatercrafts } = result.current;

    // Boats should be in the same order as in the original array
    expect(groupedWatercrafts.boat[0].id).toBe('boat-1');
    expect(groupedWatercrafts.boat[1].id).toBe('boat-2');
  });

  it('updates when watercrafts array changes', () => {
    const { result, rerender } = renderHook(
      ({ watercrafts }) => useWatercraftGrouping(watercrafts),
      { initialProps: { watercrafts: mockWatercrafts } }
    );

    // Initial state
    expect(result.current.groupedWatercrafts.boat).toHaveLength(2);

    // Add another boat
    const newWatercraft: Watercraft = {
      id: 'boat-3',
      name: 'Test Boat 3',
      type: 'boat',
      ownershipType: 'club',
      location: 'Bay F',
      skillLevel: 1,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const updatedWatercrafts = [...mockWatercrafts, newWatercraft];
    rerender({ watercrafts: updatedWatercrafts });

    expect(result.current.groupedWatercrafts.boat).toHaveLength(3);
    expect(result.current.groupedWatercrafts.boat[2].name).toBe('Test Boat 3');
  });

  it('handles watercrafts with same type but different properties', () => {
    const sameTypeWatercrafts: Watercraft[] = [
      {
        id: 'boat-1',
        name: 'Club Boat',
        type: 'boat',
        ownershipType: 'club',
        location: 'Bay A',
        skillLevel: 1,
        status: 'available',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'boat-2',
        name: 'Member Boat',
        type: 'boat',
        ownershipType: 'member',
        memberId: 'member-1',
        location: 'Bay B',
        skillLevel: 3,
        status: 'maintenance',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    const { result } = renderHook(() => useWatercraftGrouping(sameTypeWatercrafts));

    const { groupedWatercrafts } = result.current;

    expect(groupedWatercrafts.boat).toHaveLength(2);
    expect(groupedWatercrafts.boat[0].ownershipType).toBe('club');
    expect(groupedWatercrafts.boat[1].ownershipType).toBe('member');
  });

  it('memoizes result correctly', () => {
    const { result, rerender } = renderHook(
      ({ watercrafts }) => useWatercraftGrouping(watercrafts),
      { initialProps: { watercrafts: mockWatercrafts } }
    );

    const firstResult = result.current.groupedWatercrafts;

    // Rerender with same watercrafts
    rerender({ watercrafts: mockWatercrafts });

    const secondResult = result.current.groupedWatercrafts;

    // Should be the same reference due to memoization
    expect(firstResult).toBe(secondResult);
  });

  it('handles all supported watercraft types', () => {
    const allTypesWatercrafts: Watercraft[] = [
      { ...mockWatercrafts[0], type: 'boat' },
      { ...mockWatercrafts[0], id: 'kayak-1', type: 'kayak' },
      { ...mockWatercrafts[0], id: 'pb-1', type: 'PB' },
      { ...mockWatercrafts[0], id: 'launch-1', type: 'launch' },
      { ...mockWatercrafts[0], id: 'surfski-1', type: 'surfski' },
      { ...mockWatercrafts[0], id: 'coastal-1', type: 'coastal' },
    ];

    const { result } = renderHook(() => useWatercraftGrouping(allTypesWatercrafts));

    const { groupedWatercrafts } = result.current;

    expect(groupedWatercrafts).toHaveProperty('boat');
    expect(groupedWatercrafts).toHaveProperty('kayak');
    expect(groupedWatercrafts).toHaveProperty('PB');
    expect(groupedWatercrafts).toHaveProperty('launch');
    expect(groupedWatercrafts).toHaveProperty('surfski');
    expect(groupedWatercrafts).toHaveProperty('coastal');

    Object.values(groupedWatercrafts).forEach(group => {
      expect(group).toHaveLength(1);
    });
  });
});
