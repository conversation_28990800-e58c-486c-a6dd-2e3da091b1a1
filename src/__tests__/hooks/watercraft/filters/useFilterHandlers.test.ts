import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useFilterHandlers } from '@/components/watercraft/filters/hooks/useFilterHandlers';
import { WatercraftFilters } from '@/types';

describe('useFilterHandlers', () => {
  const mockOnFilterChange = vi.fn();
  const initialFilters: WatercraftFilters = {
    search: '',
    type: [],
    boatTypes: [],
    status: [],
    skillLevel: [0, 5] as [number, number],
    weightRange: [50, 100] as [number, number],
    showFavoritesOnly: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('handles type toggle correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleTypeToggle(['boat', 'kayak']);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      type: ['boat', 'kayak'],
    });
  });

  it('handles boat type toggle correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleBoatTypeToggle(['1x', '2x']);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      boatTypes: ['1x', '2x'],
    });
  });

  it('handles status toggle correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleStatusToggle(['available', 'in-use']);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      status: ['available', 'in-use'],
    });
  });

  it('handles skill level change correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleSkillLevelChange([1, 3]);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      skillLevel: [1, 3],
    });
  });

  it('handles weight range change correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleWeightRangeChange([60, 90]);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      weightRange: [60, 90],
    });
  });

  it('handles favorites toggle correctly when false', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleFavoritesToggle();
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      showFavoritesOnly: true,
    });
  });

  it('handles favorites toggle correctly when true', () => {
    const filtersWithFavorites = { ...initialFilters, showFavoritesOnly: true };
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: filtersWithFavorites, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleFavoritesToggle();
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...filtersWithFavorites,
      showFavoritesOnly: false,
    });
  });

  it('preserves other filter properties when updating one filter', () => {
    const filtersWithData: WatercraftFilters = {
      search: 'test search',
      type: ['boat'],
      boatTypes: ['1x'],
      status: ['available'],
      skillLevel: [1, 3] as [number, number],
      weightRange: [60, 80] as [number, number],
      showFavoritesOnly: true,
    };

    const { result } = renderHook(() => 
      useFilterHandlers({ filters: filtersWithData, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleTypeToggle(['kayak']);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...filtersWithData,
      type: ['kayak'], // Only this should change
    });
  });

  it('handles empty arrays correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleTypeToggle([]);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      type: [],
    });
  });

  it('handles single values in arrays correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleStatusToggle(['maintenance']);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      status: ['maintenance'],
    });
  });

  it('handles multiple calls correctly', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleTypeToggle(['boat']);
    });

    act(() => {
      result.current.handleStatusToggle(['available']);
    });

    act(() => {
      result.current.handleFavoritesToggle();
    });

    expect(mockOnFilterChange).toHaveBeenCalledTimes(3);
  });

  it('handles edge case values for skill level', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleSkillLevelChange([0, 5]);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      skillLevel: [0, 5],
    });
  });

  it('handles edge case values for weight range', () => {
    const { result } = renderHook(() => 
      useFilterHandlers({ filters: initialFilters, onFilterChange: mockOnFilterChange })
    );

    act(() => {
      result.current.handleWeightRangeChange([0, 200]);
    });

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      ...initialFilters,
      weightRange: [0, 200],
    });
  });

  it('updates filters reference when called', () => {
    let currentFilters = initialFilters;
    const mockOnFilterChangeWithUpdate = (newFilters: WatercraftFilters) => {
      currentFilters = newFilters;
      mockOnFilterChange(newFilters);
    };

    const { result, rerender } = renderHook(() => 
      useFilterHandlers({ filters: currentFilters, onFilterChange: mockOnFilterChangeWithUpdate })
    );

    act(() => {
      result.current.handleTypeToggle(['boat']);
    });

    // Rerender with updated filters
    rerender();

    expect(currentFilters.type).toEqual(['boat']);
  });
});
