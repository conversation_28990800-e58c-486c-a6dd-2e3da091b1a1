import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useStationSearch } from '@/components/weather/hooks/useStationSearch';
import { WeatherStation } from '@/types/weather';

// Mock the weather service
const mockSearchResults: WeatherStation[] = [
  {
    id: 'station-1',
    name: 'San Francisco Bay',
    state: 'CA',
  },
  {
    id: 'station-2',
    name: 'Golden Gate',
    state: 'CA',
  },
];

vi.mock('@/services/weather.service', () => ({
  searchWeatherStations: vi.fn(),
}));

describe('useStationSearch', () => {
  const mockSearchWeatherStations = vi.mocked(
    (await import('@/services/weather.service')).searchWeatherStations
  );

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('initializes with empty state', () => {
    const { result } = renderHook(() => useStationSearch());

    expect(result.current.query).toBe('');
    expect(result.current.searchResults).toEqual([]);
    expect(result.current.loading).toBe(false);
  });

  it('updates query when setQuery is called', () => {
    const { result } = renderHook(() => useStationSearch());

    act(() => {
      result.current.setQuery('San Francisco');
    });

    expect(result.current.query).toBe('San Francisco');
  });

  it('does not search when query is too short', async () => {
    const { result } = renderHook(() => useStationSearch());

    act(() => {
      result.current.setQuery('S');
    });

    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(mockSearchWeatherStations).not.toHaveBeenCalled();
    expect(result.current.searchResults).toEqual([]);
    expect(result.current.loading).toBe(false);
  });

  it('searches when query is long enough', async () => {
    mockSearchWeatherStations.mockResolvedValue(mockSearchResults);
    const { result } = renderHook(() => useStationSearch());

    act(() => {
      result.current.setQuery('San Francisco');
    });

    expect(result.current.loading).toBe(true);

    act(() => {
      vi.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(mockSearchWeatherStations).toHaveBeenCalledWith('San Francisco');
    });

    await waitFor(() => {
      expect(result.current.searchResults).toEqual(mockSearchResults);
      expect(result.current.loading).toBe(false);
    });
  });

  it('debounces search requests', async () => {
    mockSearchWeatherStations.mockResolvedValue(mockSearchResults);
    const { result } = renderHook(() => useStationSearch());

    // Rapid query changes
    act(() => {
      result.current.setQuery('San');
    });

    act(() => {
      result.current.setQuery('San F');
    });

    act(() => {
      result.current.setQuery('San Francisco');
    });

    // Only advance timer once
    act(() => {
      vi.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(mockSearchWeatherStations).toHaveBeenCalledTimes(1);
      expect(mockSearchWeatherStations).toHaveBeenCalledWith('San Francisco');
    });
  });

  it('clears results when query becomes too short', () => {
    const { result } = renderHook(() => useStationSearch());

    // Set a long query first
    act(() => {
      result.current.setQuery('San Francisco');
    });

    // Then make it short
    act(() => {
      result.current.setQuery('S');
    });

    expect(result.current.searchResults).toEqual([]);
    expect(result.current.loading).toBe(false);
  });

  it('handles search errors gracefully', async () => {
    const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
    mockSearchWeatherStations.mockRejectedValue(new Error('Search failed'));
    
    const { result } = renderHook(() => useStationSearch());

    act(() => {
      result.current.setQuery('San Francisco');
    });

    act(() => {
      vi.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.searchResults).toEqual([]);
      expect(result.current.loading).toBe(false);
    });

    expect(consoleError).toHaveBeenCalledWith('Error searching stations:', expect.any(Error));
    consoleError.mockRestore();
  });

  it('cancels previous search when new query is set', async () => {
    mockSearchWeatherStations.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve(mockSearchResults), 1000))
    );

    const { result } = renderHook(() => useStationSearch());

    act(() => {
      result.current.setQuery('San Francisco');
    });

    act(() => {
      vi.advanceTimersByTime(300);
    });

    // Change query before first search completes
    act(() => {
      result.current.setQuery('Golden Gate');
    });

    act(() => {
      vi.advanceTimersByTime(300);
    });

    // Should only call search for the latest query
    await waitFor(() => {
      expect(mockSearchWeatherStations).toHaveBeenCalledWith('Golden Gate');
    });
  });

  it('sets loading to false after successful search', async () => {
    mockSearchWeatherStations.mockResolvedValue(mockSearchResults);
    const { result } = renderHook(() => useStationSearch());

    act(() => {
      result.current.setQuery('San Francisco');
    });

    expect(result.current.loading).toBe(true);

    act(() => {
      vi.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
  });

  it('clears results when query is empty', () => {
    const { result } = renderHook(() => useStationSearch());

    // Set query and then clear it
    act(() => {
      result.current.setQuery('San Francisco');
    });

    act(() => {
      result.current.setQuery('');
    });

    expect(result.current.searchResults).toEqual([]);
    expect(result.current.loading).toBe(false);
  });
});
