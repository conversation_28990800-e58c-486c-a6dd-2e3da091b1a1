import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useNearbyStations } from '@/components/weather/hooks/useNearbyStations';
import { WeatherStation } from '@/types/weather';

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'weather_geolocation_not_supported': 'Geolocation not supported',
        'weather_no_stations_found_nearby': 'No stations found nearby',
        'weather_found_nearby_stations': 'Found {{count}} nearby stations',
        'weather_failed_find_nearby': 'Failed to find nearby stations',
        'weather_location_permission_error': 'Location permission error',
      };
      return translations[key]?.replace('{{count}}', '3') || key;
    },
  }),
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    info: vi.fn(),
    success: vi.fn(),
  },
}));

// Mock weather service
const mockNearbyStations: WeatherStation[] = [
  { id: 'station-1', name: 'Nearby Station 1', state: 'CA', distance: 2.5 },
  { id: 'station-2', name: 'Nearby Station 2', state: 'CA', distance: 5.1 },
  { id: 'station-3', name: 'Nearby Station 3', state: 'CA', distance: 8.3 },
];

vi.mock('@/services/weather.service', () => ({
  findNearbyStations: vi.fn(),
}));

describe('useNearbyStations', () => {
  const mockFindNearbyStations = vi.mocked(
    (await import('@/services/weather.service')).findNearbyStations
  );
  const mockToast = vi.mocked((await import('sonner')).toast);

  // Mock geolocation
  const mockGeolocation = {
    getCurrentPosition: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    Object.defineProperty(global, 'navigator', {
      value: { geolocation: mockGeolocation },
      writable: true,
    });
  });

  it('initializes with empty state', () => {
    const { result } = renderHook(() => useNearbyStations());

    expect(result.current.nearbyStations).toEqual([]);
    expect(result.current.locationLoading).toBe(false);
  });

  it('shows error when geolocation is not supported', async () => {
    Object.defineProperty(global, 'navigator', {
      value: {},
      writable: true,
    });

    const { result } = renderHook(() => useNearbyStations());

    await act(async () => {
      result.current.handleFindNearby();
    });

    expect(mockToast.error).toHaveBeenCalledWith('Geolocation not supported');
  });

  it('sets loading state when finding nearby stations', async () => {
    const { result } = renderHook(() => useNearbyStations());

    act(() => {
      result.current.handleFindNearby();
    });

    expect(result.current.locationLoading).toBe(true);
  });

  it('successfully finds nearby stations', async () => {
    mockFindNearbyStations.mockResolvedValue(mockNearbyStations);
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success({
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
        },
      });
    });

    const { result } = renderHook(() => useNearbyStations());

    await act(async () => {
      result.current.handleFindNearby();
    });

    await waitFor(() => {
      expect(result.current.nearbyStations).toEqual(mockNearbyStations);
      expect(result.current.locationLoading).toBe(false);
    });

    expect(mockFindNearbyStations).toHaveBeenCalledWith(37.7749, -122.4194, 100);
    expect(mockToast.success).toHaveBeenCalledWith('Found 3 nearby stations');
  });

  it('shows info message when no stations found', async () => {
    mockFindNearbyStations.mockResolvedValue([]);
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success({
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
        },
      });
    });

    const { result } = renderHook(() => useNearbyStations());

    await act(async () => {
      result.current.handleFindNearby();
    });

    await waitFor(() => {
      expect(result.current.nearbyStations).toEqual([]);
      expect(result.current.locationLoading).toBe(false);
    });

    expect(mockToast.info).toHaveBeenCalledWith('No stations found nearby');
  });

  it('handles geolocation errors', async () => {
    const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
    mockGeolocation.getCurrentPosition.mockImplementation((success, error) => {
      error({ code: 1, message: 'Permission denied' });
    });

    const { result } = renderHook(() => useNearbyStations());

    await act(async () => {
      result.current.handleFindNearby();
    });

    await waitFor(() => {
      expect(result.current.locationLoading).toBe(false);
    });

    expect(mockToast.error).toHaveBeenCalledWith('Location permission error');
    expect(consoleError).toHaveBeenCalledWith('Geolocation error:', expect.any(Object));
    consoleError.mockRestore();
  });

  it('handles service errors', async () => {
    const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
    mockFindNearbyStations.mockRejectedValue(new Error('Service error'));
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success({
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
        },
      });
    });

    const { result } = renderHook(() => useNearbyStations());

    await act(async () => {
      result.current.handleFindNearby();
    });

    await waitFor(() => {
      expect(result.current.locationLoading).toBe(false);
    });

    expect(mockToast.error).toHaveBeenCalledWith('Failed to find nearby stations');
    expect(consoleError).toHaveBeenCalledWith('Error finding nearby stations:', expect.any(Error));
    consoleError.mockRestore();
  });

  it('uses correct geolocation options', async () => {
    mockFindNearbyStations.mockResolvedValue(mockNearbyStations);
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success({
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
        },
      });
    });

    const { result } = renderHook(() => useNearbyStations());

    await act(async () => {
      result.current.handleFindNearby();
    });

    expect(mockGeolocation.getCurrentPosition).toHaveBeenCalledWith(
      expect.any(Function),
      expect.any(Function),
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
      }
    );
  });

  it('searches within 100km radius', async () => {
    mockFindNearbyStations.mockResolvedValue(mockNearbyStations);
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success({
        coords: {
          latitude: 37.7749,
          longitude: -122.4194,
        },
      });
    });

    const { result } = renderHook(() => useNearbyStations());

    await act(async () => {
      result.current.handleFindNearby();
    });

    expect(mockFindNearbyStations).toHaveBeenCalledWith(37.7749, -122.4194, 100);
  });
});
