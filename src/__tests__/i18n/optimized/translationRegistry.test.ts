import { describe, it, expect, beforeEach } from 'vitest';
import { 
  translationRegistry, 
  registerLanguage, 
  getTranslation,
  validateTranslations,
  getTranslationCompleteness 
} from '@/i18n/optimized/translationRegistry';
import { SupportedLanguage } from '@/types/language';

describe('TranslationRegistry', () => {
  beforeEach(() => {
    // Clear registry before each test
    (translationRegistry as any).registry.clear();
    (translationRegistry as any).referenceKeys.clear();
  });

  const mockEnglishTranslations = {
    'hello': 'Hello',
    'goodbye': 'Goodbye',
    'welcome': 'Welcome',
    'thank_you': 'Thank you',
  };

  const mockSpanishTranslations = {
    'hello': '<PERSON><PERSON>',
    'goodbye': '<PERSON><PERSON><PERSON>',
    'welcome': 'Bienvenido',
    // Missing 'thank_you' to test incomplete translations
  };

  const mockFrenchTranslations = {
    'hello': 'Bonjour',
    'goodbye': 'Au revoir',
    'welcome': 'Bienvenue',
    'thank_you': 'Merci',
    'extra_key': 'Extra value', // Extra key to test validation
  };

  it('registers language translation sets correctly', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    const translations = translationRegistry.getTranslations('en');
    expect(translations).toEqual(mockEnglishTranslations);
  });

  it('returns reference language when requested language not found', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    const translations = translationRegistry.getTranslations('unknown' as SupportedLanguage);
    expect(translations).toEqual(mockEnglishTranslations);
  });

  it('gets specific translation correctly', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    const translation = getTranslation('en', 'hello' as any);
    expect(translation).toBe('Hello');
  });

  it('returns key when translation not found', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    const translation = getTranslation('en', 'nonexistent_key' as any);
    expect(translation).toBe('nonexistent_key');
  });

  it('calculates completeness report correctly', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    registerLanguage({
      language: 'es',
      translations: mockSpanishTranslations,
      completeness: 75,
      lastUpdated: new Date(),
    });

    registerLanguage({
      language: 'fr',
      translations: mockFrenchTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    const report = getTranslationCompleteness();

    expect(report.es.completeness).toBe(75);
    expect(report.es.missingKeys).toContain('thank_you');
    expect(report.es.extraKeys).toEqual([]);

    expect(report.fr.completeness).toBe(100);
    expect(report.fr.missingKeys).toEqual([]);
    expect(report.fr.extraKeys).toContain('extra_key');
  });

  it('validates translations correctly', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    registerLanguage({
      language: 'es',
      translations: mockSpanishTranslations,
      completeness: 75,
      lastUpdated: new Date(),
    });

    const validation = validateTranslations();

    expect(validation.isValid).toBe(false);
    expect(validation.issues).toContain('es: Missing 1 keys');
    expect(validation.summary.en).toBe(100);
    expect(validation.summary.es).toBe(75);
  });

  it('returns supported languages correctly', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    registerLanguage({
      language: 'es',
      translations: mockSpanishTranslations,
      completeness: 75,
      lastUpdated: new Date(),
    });

    const supportedLanguages = translationRegistry.getSupportedLanguages();
    expect(supportedLanguages).toContain('en');
    expect(supportedLanguages).toContain('es');
    expect(supportedLanguages).toHaveLength(2);
  });

  it('identifies incomplete languages correctly', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    registerLanguage({
      language: 'es',
      translations: mockSpanishTranslations,
      completeness: 75,
      lastUpdated: new Date(),
    });

    const incompleteLanguages = translationRegistry.getIncompleteLanguages(90);
    expect(incompleteLanguages).toContain('es');
    expect(incompleteLanguages).not.toContain('en');
  });

  it('provides correct fallback chain', () => {
    const fallbackChain = translationRegistry.getFallbackChain('es');
    expect(fallbackChain).toEqual(['es', 'en']);

    const unknownFallback = translationRegistry.getFallbackChain('unknown' as SupportedLanguage);
    expect(unknownFallback).toEqual(['en']);
  });

  it('gets translation with fallback support', () => {
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    registerLanguage({
      language: 'es',
      translations: mockSpanishTranslations,
      completeness: 75,
      lastUpdated: new Date(),
    });

    // Should get Spanish translation
    const spanishHello = translationRegistry.getTranslationWithFallback('es', 'hello' as any);
    expect(spanishHello).toBe('Hola');

    // Should fallback to English for missing key
    const spanishThankYou = translationRegistry.getTranslationWithFallback('es', 'thank_you' as any);
    expect(spanishThankYou).toBe('Thank you');

    // Should return key if not found in any fallback
    const nonexistent = translationRegistry.getTranslationWithFallback('es', 'nonexistent' as any);
    expect(nonexistent).toBe('nonexistent');
  });

  it('handles empty translations gracefully', () => {
    registerLanguage({
      language: 'en',
      translations: {},
      completeness: 0,
      lastUpdated: new Date(),
    });

    const translations = translationRegistry.getTranslations('en');
    expect(translations).toEqual({});

    const translation = getTranslation('en', 'any_key' as any);
    expect(translation).toBe('any_key');
  });

  it('updates reference keys when English is registered', () => {
    registerLanguage({
      language: 'es',
      translations: mockSpanishTranslations,
      completeness: 75,
      lastUpdated: new Date(),
    });

    // Register English after Spanish
    registerLanguage({
      language: 'en',
      translations: mockEnglishTranslations,
      completeness: 100,
      lastUpdated: new Date(),
    });

    const report = getTranslationCompleteness();
    expect(report.es.missingKeys).toContain('thank_you');
  });
});
