import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { StationItem } from '@/components/weather/components/StationItem';
import { WeatherStation } from '@/types/weather';

// Mock the weather context
vi.mock('@/context/WeatherContext', () => ({
  useWeather: () => ({
    weatherUnit: 'metric',
  }),
}));

// Mock the StationCapabilityIcons component
vi.mock('@/components/weather/components/StationCapabilityIcons', () => ({
  StationCapabilityIcons: ({ station }: { station: WeatherStation }) => (
    <div data-testid="capability-icons">{station.name} capabilities</div>
  ),
}));

describe('StationItem', () => {
  const mockStation: WeatherStation = {
    id: 'test-station-1',
    name: 'Test Weather Station',
    state: 'CA',
    distance: 5.5,
    capabilities: {
      airTemperature: true,
      waterTemperature: false,
      wind: true,
      waterLevel: false,
      barometricPressure: true,
      visibility: false,
    },
  };

  const mockProps = {
    station: mockStation,
    onSelect: vi.fn(),
    type: 'nearby' as const,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders station name', () => {
    render(<StationItem {...mockProps} />);
    
    expect(screen.getByText('Test Weather Station')).toBeInTheDocument();
  });

  it('renders station state when provided', () => {
    render(<StationItem {...mockProps} />);
    
    expect(screen.getByText('(CA)')).toBeInTheDocument();
  });

  it('does not render state when not provided', () => {
    const stationWithoutState = { ...mockStation, state: undefined };
    const props = { ...mockProps, station: stationWithoutState };
    render(<StationItem {...props} />);
    
    expect(screen.queryByText(/\(/)).not.toBeInTheDocument();
  });

  it('renders capability icons', () => {
    render(<StationItem {...mockProps} />);
    
    expect(screen.getByTestId('capability-icons')).toBeInTheDocument();
    expect(screen.getByText('Test Weather Station capabilities')).toBeInTheDocument();
  });

  it('displays distance in kilometers for metric units', () => {
    render(<StationItem {...mockProps} />);
    
    expect(screen.getByText('5.5 km')).toBeInTheDocument();
  });

  it('displays distance in miles for imperial units', () => {
    // Mock imperial units
    vi.mocked(vi.importMock('@/context/WeatherContext')).useWeather.mockReturnValue({
      weatherUnit: 'imperial',
    });

    render(<StationItem {...mockProps} />);
    
    // 5.5 km = ~3.4 miles
    expect(screen.getByText('3.4 mi')).toBeInTheDocument();
  });

  it('does not display distance when not provided', () => {
    const stationWithoutDistance = { ...mockStation, distance: undefined };
    const props = { ...mockProps, station: stationWithoutDistance };
    render(<StationItem {...props} />);
    
    expect(screen.queryByText(/km|mi/)).not.toBeInTheDocument();
  });

  it('uses Navigation icon for nearby type', () => {
    render(<StationItem {...mockProps} />);
    
    const icon = screen.getByRole('button').querySelector('svg');
    expect(icon).toHaveClass('text-blue-600');
  });

  it('uses MapPin icon for search type', () => {
    const searchProps = { ...mockProps, type: 'search' as const };
    render(<StationItem {...searchProps} />);
    
    const icon = screen.getByRole('button').querySelector('svg');
    expect(icon).toHaveClass('text-green-600');
  });

  it('calls onSelect when clicked', () => {
    render(<StationItem {...mockProps} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockProps.onSelect).toHaveBeenCalledWith(mockStation);
  });

  it('has correct command item value', () => {
    render(<StationItem {...mockProps} />);
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('data-value', 'nearby-test-station-1-Test Weather Station');
  });

  it('formats distance correctly for different values', () => {
    const testCases = [
      { distance: 0.5, expected: '0.5 km' },
      { distance: 10.0, expected: '10.0 km' },
      { distance: 25.7, expected: '25.7 km' },
    ];

    testCases.forEach(({ distance, expected }) => {
      const stationWithDistance = { ...mockStation, distance };
      const props = { ...mockProps, station: stationWithDistance };
      const { rerender } = render(<StationItem {...props} />);
      
      expect(screen.getByText(expected)).toBeInTheDocument();
      
      rerender(<div />); // Clear for next test
    });
  });

  it('handles long station names gracefully', () => {
    const longNameStation = {
      ...mockStation,
      name: 'Very Long Weather Station Name That Might Overflow',
    };
    const props = { ...mockProps, station: longNameStation };
    render(<StationItem {...props} />);
    
    expect(screen.getByText('Very Long Weather Station Name That Might Overflow')).toBeInTheDocument();
  });

  it('handles stations without capabilities', () => {
    const stationWithoutCapabilities = { ...mockStation, capabilities: undefined };
    const props = { ...mockProps, station: stationWithoutCapabilities };
    render(<StationItem {...props} />);
    
    // Should still render the station name and other info
    expect(screen.getByText('Test Weather Station')).toBeInTheDocument();
    expect(screen.getByTestId('capability-icons')).toBeInTheDocument();
  });
});
