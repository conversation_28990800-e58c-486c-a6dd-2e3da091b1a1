import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { StationCapabilityIcons } from '@/components/weather/components/StationCapabilityIcons';
import { WeatherStation } from '@/types/weather';

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'weather_capability_air_temperature': 'Air Temperature',
        'weather_capability_water_temperature': 'Water Temperature',
        'weather_capability_wind_data': 'Wind Data',
        'weather_capability_water_level': 'Water Level',
        'weather_capability_barometric_pressure': 'Barometric Pressure',
        'weather_capability_visibility': 'Visibility',
      };
      return translations[key] || key;
    },
  }),
}));

describe('StationCapabilityIcons', () => {
  const createMockStation = (capabilities: any): WeatherStation => ({
    id: 'test-station',
    name: 'Test Station',
    state: 'CA',
    capabilities,
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns null when station has no capabilities', () => {
    const station = createMockStation(null);
    const { container } = render(<StationCapabilityIcons station={station} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('returns null when station capabilities are undefined', () => {
    const station = createMockStation(undefined);
    const { container } = render(<StationCapabilityIcons station={station} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('renders air temperature icon when capability is true', () => {
    const station = createMockStation({ airTemperature: true });
    render(<StationCapabilityIcons station={station} />);
    
    const icon = screen.getByTitle('Air Temperature');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-orange-500');
  });

  it('renders water temperature icon when capability is true', () => {
    const station = createMockStation({ waterTemperature: true });
    render(<StationCapabilityIcons station={station} />);
    
    const icon = screen.getByTitle('Water Temperature');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-blue-500');
  });

  it('renders wind icon when capability is true', () => {
    const station = createMockStation({ wind: true });
    render(<StationCapabilityIcons station={station} />);
    
    const icon = screen.getByTitle('Wind Data');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-sky-500');
  });

  it('renders water level icon when capability is true', () => {
    const station = createMockStation({ waterLevel: true });
    render(<StationCapabilityIcons station={station} />);
    
    const icon = screen.getByTitle('Water Level');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-teal-500');
  });

  it('renders barometric pressure icon when capability is true', () => {
    const station = createMockStation({ barometricPressure: true });
    render(<StationCapabilityIcons station={station} />);
    
    const icon = screen.getByTitle('Barometric Pressure');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-green-500');
  });

  it('renders visibility icon when capability is true', () => {
    const station = createMockStation({ visibility: true });
    render(<StationCapabilityIcons station={station} />);
    
    const icon = screen.getByTitle('Visibility');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-purple-500');
  });

  it('renders multiple icons when multiple capabilities are true', () => {
    const station = createMockStation({
      airTemperature: true,
      waterTemperature: true,
      wind: true,
    });
    render(<StationCapabilityIcons station={station} />);
    
    expect(screen.getByTitle('Air Temperature')).toBeInTheDocument();
    expect(screen.getByTitle('Water Temperature')).toBeInTheDocument();
    expect(screen.getByTitle('Wind Data')).toBeInTheDocument();
  });

  it('does not render icons for false capabilities', () => {
    const station = createMockStation({
      airTemperature: false,
      waterTemperature: false,
      wind: true,
    });
    render(<StationCapabilityIcons station={station} />);
    
    expect(screen.queryByTitle('Air Temperature')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Water Temperature')).not.toBeInTheDocument();
    expect(screen.getByTitle('Wind Data')).toBeInTheDocument();
  });

  it('renders container with correct CSS classes when icons are present', () => {
    const station = createMockStation({ airTemperature: true });
    render(<StationCapabilityIcons station={station} />);
    
    const container = screen.getByTitle('Air Temperature').parentElement;
    expect(container).toHaveClass('flex', 'gap-1', 'mt-1');
  });

  it('returns null when all capabilities are false', () => {
    const station = createMockStation({
      airTemperature: false,
      waterTemperature: false,
      wind: false,
      waterLevel: false,
      barometricPressure: false,
      visibility: false,
    });
    const { container } = render(<StationCapabilityIcons station={station} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('handles mixed true/false capabilities correctly', () => {
    const station = createMockStation({
      airTemperature: true,
      waterTemperature: false,
      wind: true,
      waterLevel: false,
      barometricPressure: true,
      visibility: false,
    });
    render(<StationCapabilityIcons station={station} />);
    
    // Should render 3 icons
    expect(screen.getByTitle('Air Temperature')).toBeInTheDocument();
    expect(screen.getByTitle('Wind Data')).toBeInTheDocument();
    expect(screen.getByTitle('Barometric Pressure')).toBeInTheDocument();
    
    // Should not render these
    expect(screen.queryByTitle('Water Temperature')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Water Level')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Visibility')).not.toBeInTheDocument();
  });
});
