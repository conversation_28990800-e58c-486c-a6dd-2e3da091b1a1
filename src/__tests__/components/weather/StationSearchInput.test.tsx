import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { StationSearchInput } from '@/components/weather/components/StationSearchInput';

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'weather_search_placeholder': 'Search weather stations...',
        'weather_find_nearby_stations': 'Find nearby stations',
      };
      return translations[key] || key;
    },
  }),
}));

describe('StationSearchInput', () => {
  const mockProps = {
    query: '',
    onQueryChange: vi.fn(),
    onFindNearby: vi.fn(),
    locationLoading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders search input with placeholder', () => {
    render(<StationSearchInput {...mockProps} />);
    
    const input = screen.getByPlaceholderText('Search weather stations...');
    expect(input).toBeInTheDocument();
  });

  it('renders find nearby button', () => {
    render(<StationSearchInput {...mockProps} />);
    
    const button = screen.getByTitle('Find nearby stations');
    expect(button).toBeInTheDocument();
  });

  it('calls onQueryChange when input value changes', () => {
    render(<StationSearchInput {...mockProps} />);
    
    const input = screen.getByPlaceholderText('Search weather stations...');
    fireEvent.change(input, { target: { value: 'test query' } });
    
    expect(mockProps.onQueryChange).toHaveBeenCalledWith('test query');
  });

  it('calls onFindNearby when nearby button is clicked', () => {
    render(<StationSearchInput {...mockProps} />);
    
    const button = screen.getByTitle('Find nearby stations');
    fireEvent.click(button);
    
    expect(mockProps.onFindNearby).toHaveBeenCalled();
  });

  it('displays current query value', () => {
    const propsWithQuery = { ...mockProps, query: 'San Francisco' };
    render(<StationSearchInput {...propsWithQuery} />);
    
    const input = screen.getByDisplayValue('San Francisco');
    expect(input).toBeInTheDocument();
  });

  it('shows loading spinner when locationLoading is true', () => {
    const loadingProps = { ...mockProps, locationLoading: true };
    render(<StationSearchInput {...loadingProps} />);
    
    // The loading spinner should be present (Loader2 icon)
    const button = screen.getByTitle('Find nearby stations');
    expect(button).toBeDisabled();
  });

  it('disables nearby button when loading', () => {
    const loadingProps = { ...mockProps, locationLoading: true };
    render(<StationSearchInput {...loadingProps} />);
    
    const button = screen.getByTitle('Find nearby stations');
    expect(button).toBeDisabled();
  });

  it('enables nearby button when not loading', () => {
    render(<StationSearchInput {...mockProps} />);
    
    const button = screen.getByTitle('Find nearby stations');
    expect(button).not.toBeDisabled();
  });

  it('has correct CSS classes for styling', () => {
    render(<StationSearchInput {...mockProps} />);
    
    const container = screen.getByPlaceholderText('Search weather stations...').parentElement;
    expect(container).toHaveClass('flex', 'items-center', 'border-b', 'px-3');
  });

  it('handles empty query gracefully', () => {
    const emptyProps = { ...mockProps, query: '' };
    render(<StationSearchInput {...emptyProps} />);
    
    const input = screen.getByPlaceholderText('Search weather stations...');
    expect(input).toHaveValue('');
  });
});
