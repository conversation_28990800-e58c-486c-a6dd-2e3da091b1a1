import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { WatercraftGrid } from '@/components/watercraft/components/WatercraftGrid';
import { Watercraft } from '@/types';

// Mock the WatercraftCard component
vi.mock('@/components/watercraft/WatercraftCard', () => ({
  default: ({ watercraft, onToggleFavorite, onScheduleJaunt, onViewDetails }: any) => (
    <div data-testid={`watercraft-card-${watercraft.id}`}>
      <h3>{watercraft.name}</h3>
      <button onClick={onToggleFavorite} data-testid={`favorite-${watercraft.id}`}>
        Toggle Favorite
      </button>
      <button onClick={onScheduleJaunt} data-testid={`checkout-${watercraft.id}`}>
        Schedule Jaunt
      </button>
      <button onClick={onViewDetails} data-testid={`details-${watercraft.id}`}>
        View Details
      </button>
    </div>
  ),
}));

describe('WatercraftGrid', () => {
  const mockWatercrafts: Watercraft[] = [
    {
      id: 'watercraft-1',
      name: 'Test Boat 1',
      type: 'boat',
      ownershipType: 'club',
      location: 'Bay A',
      skillLevel: 1,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'watercraft-2',
      name: 'Test Kayak 1',
      type: 'kayak',
      ownershipType: 'club',
      location: 'Bay B',
      skillLevel: 0,
      status: 'in-use',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'watercraft-3',
      name: 'Test Surfski 1',
      type: 'surfski',
      ownershipType: 'member',
      memberId: 'member-1',
      location: 'Bay C',
      skillLevel: 2,
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  const mockProps = {
    watercrafts: mockWatercrafts,
    onToggleFavorite: vi.fn(),
    onCheckout: vi.fn(),
    onViewDetails: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all watercraft cards', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    expect(screen.getByTestId('watercraft-card-watercraft-1')).toBeInTheDocument();
    expect(screen.getByTestId('watercraft-card-watercraft-2')).toBeInTheDocument();
    expect(screen.getByTestId('watercraft-card-watercraft-3')).toBeInTheDocument();
  });

  it('displays watercraft names', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    expect(screen.getByText('Test Boat 1')).toBeInTheDocument();
    expect(screen.getByText('Test Kayak 1')).toBeInTheDocument();
    expect(screen.getByText('Test Surfski 1')).toBeInTheDocument();
  });

  it('has correct grid layout classes', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    const gridContainer = screen.getByTestId('watercraft-card-watercraft-1').parentElement;
    expect(gridContainer).toHaveClass(
      'grid',
      'grid-cols-1',
      'md:grid-cols-2',
      'lg:grid-cols-3',
      'xl:grid-cols-4',
      'gap-4'
    );
  });

  it('passes correct props to WatercraftCard components', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    // Check that buttons are rendered (indicating props are passed correctly)
    expect(screen.getByTestId('favorite-watercraft-1')).toBeInTheDocument();
    expect(screen.getByTestId('checkout-watercraft-1')).toBeInTheDocument();
    expect(screen.getByTestId('details-watercraft-1')).toBeInTheDocument();
  });

  it('calls onToggleFavorite with correct watercraft id', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    const favoriteButton = screen.getByTestId('favorite-watercraft-1');
    favoriteButton.click();
    
    expect(mockProps.onToggleFavorite).toHaveBeenCalledWith('watercraft-1');
  });

  it('calls onCheckout with correct watercraft object', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    const checkoutButton = screen.getByTestId('checkout-watercraft-2');
    checkoutButton.click();
    
    expect(mockProps.onCheckout).toHaveBeenCalledWith(mockWatercrafts[1]);
  });

  it('calls onViewDetails with correct watercraft object', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    const detailsButton = screen.getByTestId('details-watercraft-3');
    detailsButton.click();
    
    expect(mockProps.onViewDetails).toHaveBeenCalledWith(mockWatercrafts[2]);
  });

  it('renders empty grid when no watercrafts provided', () => {
    const emptyProps = { ...mockProps, watercrafts: [] };
    const { container } = render(<WatercraftGrid {...emptyProps} />);

    const gridContainer = container.querySelector('.grid');
    expect(gridContainer).toHaveClass('grid');
    expect(gridContainer?.children).toHaveLength(0);
  });

  it('handles single watercraft correctly', () => {
    const singleWatercraftProps = {
      ...mockProps,
      watercrafts: [mockWatercrafts[0]],
    };
    render(<WatercraftGrid {...singleWatercraftProps} />);
    
    expect(screen.getByTestId('watercraft-card-watercraft-1')).toBeInTheDocument();
    expect(screen.queryByTestId('watercraft-card-watercraft-2')).not.toBeInTheDocument();
    expect(screen.queryByTestId('watercraft-card-watercraft-3')).not.toBeInTheDocument();
  });

  it('maintains correct key prop for each watercraft card', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    // Each card should be rendered with unique keys (React requirement)
    // This is tested implicitly by ensuring all cards render correctly
    expect(screen.getAllByText(/Test (Boat|Kayak|Surfski) 1/)).toHaveLength(3);
  });

  it('handles different watercraft types correctly', () => {
    render(<WatercraftGrid {...mockProps} />);
    
    // All different types should be rendered
    expect(screen.getByText('Test Boat 1')).toBeInTheDocument(); // boat
    expect(screen.getByText('Test Kayak 1')).toBeInTheDocument(); // kayak
    expect(screen.getByText('Test Surfski 1')).toBeInTheDocument(); // surfski
  });
});
