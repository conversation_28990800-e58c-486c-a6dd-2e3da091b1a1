import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { WatercraftSkeleton, WatercraftLoadingState } from '@/components/watercraft/components/WatercraftSkeleton';

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'loading_watercrafts': 'Loading watercrafts...',
      };
      return translations[key] || key;
    },
  }),
}));

describe('WatercraftSkeleton', () => {
  it('renders default number of skeleton items (8)', () => {
    render(<WatercraftSkeleton />);
    
    const skeletonItems = screen.getAllByRole('generic').filter(el => 
      el.className.includes('animate-pulse')
    );
    expect(skeletonItems).toHaveLength(8);
  });

  it('renders custom number of skeleton items', () => {
    render(<WatercraftSkeleton count={5} />);
    
    const skeletonItems = screen.getAllByRole('generic').filter(el => 
      el.className.includes('animate-pulse')
    );
    expect(skeletonItems).toHaveLength(5);
  });

  it('has correct grid layout classes', () => {
    render(<WatercraftSkeleton />);
    
    const gridContainer = screen.getByRole('generic');
    expect(gridContainer).toHaveClass(
      'grid',
      'grid-cols-1',
      'md:grid-cols-2',
      'lg:grid-cols-3',
      'xl:grid-cols-4',
      'gap-4'
    );
  });

  it('skeleton items have correct styling classes', () => {
    render(<WatercraftSkeleton count={1} />);
    
    const skeletonItem = screen.getByRole('generic').children[0];
    expect(skeletonItem).toHaveClass(
      'bg-card',
      'rounded-lg',
      'shadow-sm',
      'border',
      'p-4',
      'h-64',
      'animate-pulse'
    );
  });

  it('renders skeleton elements with correct structure', () => {
    render(<WatercraftSkeleton count={1} />);
    
    const skeletonItem = screen.getByRole('generic').children[0];
    const skeletonElements = skeletonItem.querySelectorAll('.bg-muted');
    
    // Should have title, grid items, image placeholder, and buttons
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it('handles zero count gracefully', () => {
    render(<WatercraftSkeleton count={0} />);
    
    const gridContainer = screen.getByRole('generic');
    expect(gridContainer.children).toHaveLength(0);
  });
});

describe('WatercraftLoadingState', () => {
  const mockOnFilterChange = vi.fn();

  it('renders loading message', () => {
    render(<WatercraftLoadingState onFilterChange={mockOnFilterChange} />);
    
    expect(screen.getByText('Loading watercrafts...')).toBeInTheDocument();
  });

  it('renders loading spinner', () => {
    render(<WatercraftLoadingState onFilterChange={mockOnFilterChange} />);
    
    const spinner = screen.getByRole('generic').querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('renders WatercraftSkeleton component', () => {
    render(<WatercraftLoadingState onFilterChange={mockOnFilterChange} />);
    
    // Check for skeleton items
    const skeletonItems = screen.getAllByRole('generic').filter(el => 
      el.className.includes('animate-pulse')
    );
    expect(skeletonItems.length).toBeGreaterThan(0);
  });

  it('has correct container structure', () => {
    render(<WatercraftLoadingState onFilterChange={mockOnFilterChange} />);
    
    const container = screen.getByText('Loading watercrafts...').closest('.space-y-4');
    expect(container).toBeInTheDocument();
  });

  it('centers the loading message', () => {
    render(<WatercraftLoadingState onFilterChange={mockOnFilterChange} />);
    
    const loadingSection = screen.getByText('Loading watercrafts...').parentElement;
    expect(loadingSection).toHaveClass('text-center', 'py-8');
  });

  it('loading message has correct styling', () => {
    render(<WatercraftLoadingState onFilterChange={mockOnFilterChange} />);
    
    const loadingText = screen.getByText('Loading watercrafts...');
    expect(loadingText).toHaveClass('text-muted-foreground');
  });

  it('spinner has correct styling', () => {
    render(<WatercraftLoadingState onFilterChange={mockOnFilterChange} />);
    
    const spinner = screen.getByRole('generic').querySelector('.animate-spin');
    expect(spinner).toHaveClass('h-6', 'w-6', 'animate-spin', 'mx-auto', 'mb-2');
  });
});
