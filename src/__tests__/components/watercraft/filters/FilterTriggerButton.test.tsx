import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { FilterTriggerButton } from '@/components/watercraft/filters/FilterTriggerButton';

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'filters': 'Filters',
      };
      return translations[key] || key;
    },
  }),
}));

describe('FilterTriggerButton', () => {
  const mockOnClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders filter button with text', () => {
    render(<FilterTriggerButton activeFilterCount={0} onClick={mockOnClick} />);
    
    expect(screen.getByText('Filters')).toBeInTheDocument();
  });

  it('renders filter icon', () => {
    render(<FilterTriggerButton activeFilterCount={0} onClick={mockOnClick} />);
    
    const button = screen.getByRole('button');
    const icon = button.querySelector('svg');
    expect(icon).toBeInTheDocument();
  });

  it('does not show badge when activeFilterCount is 0', () => {
    render(<FilterTriggerButton activeFilterCount={0} onClick={mockOnClick} />);
    
    expect(screen.queryByText('0')).not.toBeInTheDocument();
  });

  it('shows badge when activeFilterCount is greater than 0', () => {
    render(<FilterTriggerButton activeFilterCount={3} onClick={mockOnClick} />);
    
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('badge has correct styling', () => {
    render(<FilterTriggerButton activeFilterCount={5} onClick={mockOnClick} />);
    
    const badge = screen.getByText('5');
    expect(badge).toHaveClass(
      'inline-flex',
      'items-center',
      'justify-center',
      'rounded-full',
      'bg-primary',
      'w-5',
      'h-5',
      'text-[10px]',
      'text-white',
      'font-medium'
    );
  });

  it('calls onClick when button is clicked', () => {
    render(<FilterTriggerButton activeFilterCount={0} onClick={mockOnClick} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('has correct button styling', () => {
    render(<FilterTriggerButton activeFilterCount={0} onClick={mockOnClick} />);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass(
      'flex',
      'gap-2',
      'w-full',
      'sm:w-auto',
      'justify-center'
    );
  });

  it('text has correct styling for overflow handling', () => {
    render(<FilterTriggerButton activeFilterCount={0} onClick={mockOnClick} />);
    
    const text = screen.getByText('Filters');
    expect(text).toHaveClass(
      'whitespace-nowrap',
      'overflow-hidden',
      'text-ellipsis'
    );
  });

  it('works without onClick handler', () => {
    expect(() => {
      render(<FilterTriggerButton activeFilterCount={0} />);
    }).not.toThrow();
  });

  it('handles large filter counts', () => {
    render(<FilterTriggerButton activeFilterCount={99} onClick={mockOnClick} />);
    
    expect(screen.getByText('99')).toBeInTheDocument();
  });

  it('handles single digit filter counts', () => {
    render(<FilterTriggerButton activeFilterCount={1} onClick={mockOnClick} />);
    
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('button is accessible', () => {
    render(<FilterTriggerButton activeFilterCount={2} onClick={mockOnClick} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).not.toHaveAttribute('aria-disabled');
  });

  it('renders correctly with fallback text when translation is missing', () => {
    // Mock missing translation
    vi.mocked(vi.importMock('@/context/LanguageContext')).useLanguage.mockReturnValue({
      t: () => null,
    });

    render(<FilterTriggerButton activeFilterCount={0} onClick={mockOnClick} />);
    
    expect(screen.getByText('Filters')).toBeInTheDocument();
  });
});
