import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { WatercraftEmptyState } from '@/components/watercraft/components/WatercraftEmptyState';

// Mock the language context
vi.mock('@/context/LanguageContext', () => ({
  useLanguage: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'no_watercrafts_found': 'No watercrafts found',
        'try_adjusting_filters': 'Try adjusting your filters',
      };
      return translations[key] || key;
    },
  }),
}));

describe('WatercraftEmptyState', () => {
  it('renders the empty state message', () => {
    render(<WatercraftEmptyState />);
    
    expect(screen.getByText('No watercrafts found')).toBeInTheDocument();
  });

  it('renders the filter suggestion message', () => {
    render(<WatercraftEmptyState />);
    
    expect(screen.getByText('Try adjusting your filters')).toBeInTheDocument();
  });

  it('has correct styling classes', () => {
    render(<WatercraftEmptyState />);
    
    const container = screen.getByText('No watercrafts found').parentElement;
    expect(container).toHaveClass(
      'text-center',
      'py-8',
      'bg-muted/20',
      'rounded-lg',
      'border'
    );
  });

  it('renders both messages with correct text styling', () => {
    render(<WatercraftEmptyState />);
    
    const mainMessage = screen.getByText('No watercrafts found');
    const subMessage = screen.getByText('Try adjusting your filters');
    
    expect(mainMessage).toHaveClass('text-muted-foreground');
    expect(subMessage).toHaveClass('text-sm', 'text-muted-foreground');
  });

  it('renders as a single container element', () => {
    const { container } = render(<WatercraftEmptyState />);
    
    expect(container.firstChild).toHaveClass('text-center');
    expect(container.children).toHaveLength(1);
  });
});
