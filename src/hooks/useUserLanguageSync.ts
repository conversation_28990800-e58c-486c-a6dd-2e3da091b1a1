import { useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';

/**
 * Hook to sync language preferences between user profile and language context
 *
 * This hook implements the language selection priority system:
 * 1. User Profile Language (highest priority) - overrides all other settings
 * 2. Manual Language Selection - user explicitly chose via language switcher
 * 3. Device Default Language - browser/OS language preference
 * 4. Fallback to English (lowest priority)
 *
 * When a user logs in with a language preference in their profile, it will
 * override any manual or device language selection. When they log out, the
 * system reverts to manual selection (if any) or device default language.
 *
 * This hook should be used in the main App component to ensure language
 * preferences are properly synchronized when the user logs in or changes.
 */
export const useUserLanguageSync = () => {
  const { user } = useAuth();
  const { updateLanguageFromUserProfile } = useLanguage();

  useEffect(() => {
    // When user data changes (login/logout), update language accordingly
    if (user?.language) {
      // User has a language preference in their profile
      updateLanguageFromUserProfile(user.language);
    } else if (user === null) {
      // User logged out, revert to device/manual preference
      updateLanguageFromUserProfile(undefined);
    }
  }, [user, updateLanguageFromUserProfile]);
};
