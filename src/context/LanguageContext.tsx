
import { createContext, useState, useContext, useEffect, ReactNode } from "react";
import { SupportedLanguage, LanguageContextType } from "@/types";
import { getTranslation } from "@/i18n/translations";
import { TranslationKey } from "@/i18n/types";

const defaultLanguage: SupportedLanguage = "en";
const supportedLanguages: SupportedLanguage[] = ["en", "es", "fr", "de", "ko", "he", "ru", "ja", "hi"];

/**
 * Function to detect device's preferred language
 *
 * Language Selection Priority Order:
 * 1. User Profile Language (highest priority) - set in user account settings
 * 2. Manual Language Selection - user explicitly chose via language switcher
 * 3. Device Default Language (this function) - browser/OS language preference
 * 4. Fallback to English (lowest priority)
 *
 * This function checks the browser's language preferences and returns the first
 * supported language found, or falls back to English if none are supported.
 */
const getDeviceLanguage = (): SupportedLanguage => {
  if (typeof window === 'undefined') return defaultLanguage;

  // Get the browser's language preferences in order
  const languages = navigator.languages || [navigator.language];

  for (const lang of languages) {
    // Extract the primary language code (e.g., 'en' from 'en-US')
    const primaryLang = lang.split('-')[0] as SupportedLanguage;
    if (supportedLanguages.includes(primaryLang)) {
      return primaryLang;
    }
  }

  return defaultLanguage;
};

// Updated context type with typed translation function
interface TypedLanguageContextType extends Omit<LanguageContextType, 't'> {
  t: (key: TranslationKey) => string;
  updateLanguageFromUserProfile: (userLanguage?: string) => void;
}

const LanguageContext = createContext<TypedLanguageContextType>({
  language: defaultLanguage,
  setLanguage: () => {},
  t: (key: TranslationKey) => key,
  updateLanguageFromUserProfile: () => {},
});

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguageState] = useState<SupportedLanguage>(() => {
    // Initial language selection logic:
    // 1. Check localStorage for manually set language preference
    // 2. Fall back to device's default language
    if (typeof window === 'undefined') return defaultLanguage;

    const savedLanguage = localStorage.getItem("language") as SupportedLanguage;
    // Check if it's a valid manually set language
    if (savedLanguage && supportedLanguages.includes(savedLanguage)) {
      return savedLanguage;
    }

    // Use device's preferred language
    return getDeviceLanguage();
  });

  // Custom setLanguage function that handles manual language changes
  const setLanguage = (newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage);
    // Save manual language selection to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem("language", newLanguage);
    }
  };

  // Function to update language based on user profile
  const updateLanguageFromUserProfile = (userLanguage?: string) => {
    if (userLanguage && supportedLanguages.includes(userLanguage as SupportedLanguage)) {
      // User has a language preference in their profile - this takes highest priority
      setLanguageState(userLanguage as SupportedLanguage);
      // Clear any manual override since user profile should take precedence
      if (typeof window !== 'undefined') {
        localStorage.removeItem("language");
      }
    } else {
      // No user preference, check priority order:
      // 1. Manual language selection (localStorage)
      // 2. Device default language
      const savedLanguage = localStorage.getItem("language") as SupportedLanguage;
      if (savedLanguage && supportedLanguages.includes(savedLanguage)) {
        // User has manually selected a language - keep using it
        setLanguageState(savedLanguage);
      } else {
        // No manual selection, use device language
        const deviceLang = getDeviceLanguage();
        setLanguageState(deviceLang);
      }
    }
  };

  // Update the document attributes when language changes
  useEffect(() => {
    // Set the document direction for RTL languages
    document.documentElement.dir = language === "he" ? "rtl" : "ltr";

    // Set lang attribute on html element for accessibility
    document.documentElement.lang = language;

    // Force update of all translated components
    document.dispatchEvent(new Event('languageChanged'));
  }, [language]);

  // Type-safe translation function
  const t = (key: TranslationKey): string => {
    if (!key) {
      console.warn('Empty translation key provided');
      return '';
    }
    
    const translation = getTranslation(language, key);
    
    // If the translation is not found, log a warning and return the key as fallback
    if (translation === key && process.env.NODE_ENV !== 'production') {
      console.warn(`Missing translation for key "${key}" in language "${language}"`);
    }
    
    return translation;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, updateLanguageFromUserProfile }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);
