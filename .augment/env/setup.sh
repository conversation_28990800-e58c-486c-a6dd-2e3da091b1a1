#!/bin/bash
set -e

echo "Setting up BoatBook project environment..."

# Update system packages
sudo apt-get update -y

# Install Node.js 18 (LTS) if not already installed
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'.' -f1 | cut -d'v' -f2) -lt 18 ]]; then
    echo "Installing Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verify Node.js and npm versions
echo "Node.js version: $(node -v)"
echo "npm version: $(npm -v)"

# Install frontend dependencies
echo "Installing frontend dependencies..."
npm ci --legacy-peer-deps

# Install backend dependencies
echo "Installing backend dependencies..."
cd server
npm ci
cd ..

# Add npm global bin to PATH if not already there
if ! echo $PATH | grep -q "$HOME/.npm-global/bin"; then
    echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> $HOME/.profile
    export PATH="$HOME/.npm-global/bin:$PATH"
fi

# Set environment variables for testing
export NODE_ENV=test
export VITE_API_URL=http://localhost:3001

echo "Setup complete!"
echo ""
echo "Project Analysis Summary:"
echo "========================="
echo "- Frontend: React 18 + TypeScript + Vite"
echo "- Backend: Node.js + Express"
echo "- Frontend Testing: Vitest with jsdom"
echo "- Backend Testing: Jest with supertest"
echo "- Package Manager: npm"
echo ""
echo "Available Test Commands:"
echo "- Frontend: npm test (runs vitest)"
echo "- Backend: cd server && npm test (runs jest)"
echo ""
echo "The backend tests are fully functional and passing."
echo "Some frontend tests require additional mocking setup to work properly."